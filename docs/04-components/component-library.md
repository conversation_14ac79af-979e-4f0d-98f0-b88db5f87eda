# Asger.me Component Library

## Core UI Components

### InteractiveLogo

**Purpose**: Main navigation dropdown styled as a logo
**Location**: `src/components/InteractiveLogo.tsx`

**Features**:
- Real-time availability status (online/away/offline)
- Dropdown navigation menu
- Profile information display
- Animated status indicators

**Usage**:
```tsx
<InteractiveLogo
  isScrolled={isScrolled}
  variant="default"
  className="custom-class"
/>
```

## Neo-Brutalist Components

### neo-card

**Class**: `neo-card`
**Styles**: 3px black border, 4px shadow offset, white background
**Usage**: Container for content sections

### neo-button

**Class**: `neo-button`
**Styles**: Bold border, shadow, active state animation

**Variants**:
- **Primary**: `bg-primary hover:bg-primary/90`
- **Secondary**: `bg-secondary hover:bg-secondary/90`
- **White**: `bg-white hover:bg-gray-50`

### neo-input

**Class**: `neo-input`
**Styles**: Bold border, focus ring, padding
**Usage**: Form inputs and textareas

## Service Components

### ServiceCard

**Purpose**: Display individual services with hover effects

**Props**:
- `service`: Service object with details
- `isHovered`: Boolean for hover state
- `onHover`: Callback for hover tracking

### BookingForm

**Purpose**: Multi-step form for service booking

**Features**:
- Step validation
- Progress indicator
- Data persistence
- Error handling

## Content Components

### BlogPost

**Purpose**: Display blog articles

**Features**:
- Rich text rendering
- Social sharing
- Reading time estimate
- Related posts

### TestimonialCard

**Purpose**: Client testimonials display

**Features**:
- Rating display
- Rotating animations
- Client info