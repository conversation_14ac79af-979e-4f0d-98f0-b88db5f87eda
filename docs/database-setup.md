# Database Setup Documentation

## Overview

This project uses **Neon.tech** as the primary database provider. Neon is a serverless PostgreSQL platform that offers modern developer features like autoscaling, branching, and instant restore.

## Database Provider: Neon.tech

### Why Neon?
- **Serverless PostgreSQL**: Automatic scaling and pay-per-use pricing
- **Branching**: Create database branches for development and testing
- **Modern Features**: Built for modern development workflows
- **Performance**: Optimized for serverless and edge environments
- **Security**: SSL connections with channel binding by default

## Setup Instructions

### 1. Create Neon Account
1. Visit [neon.tech](https://neon.tech)
2. Sign up for a free account
3. Create a new project/database

### 2. Get Connection String
From your Neon dashboard, copy the connection string which looks like:
```
postgresql://username:<EMAIL>/dbname?sslmode=require&channel_binding=require
```

### 3. Environment Configuration
1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your actual Neon connection string:
   ```env
   DATABASE_URL="************************************************************************************************************"
   VITE_DATABASE_URL="************************************************************************************************************"
   ```

### 4. Install Dependencies
The Neon serverless driver is already installed:
```bash
npm install @neondatabase/serverless
```

## Database Client Usage

### Basic Connection
```typescript
import { sql } from '@/integrations/neon/client';

// Simple query
const users = await sql`SELECT * FROM users`;

// Parameterized query
const user = await sql`SELECT * FROM users WHERE id = ${userId}`;

// Insert with returning
const newUser = await sql`
  INSERT INTO users (email, name) 
  VALUES (${email}, ${name}) 
  RETURNING *
`;
```

### Error Handling
```typescript
try {
  const result = await sql`SELECT * FROM users`;
  console.log('Success:', result);
} catch (error) {
  console.error('Database error:', error);
}
```

## Testing the Connection

### Using the Test Interface
1. Start the development server: `npm run dev`
2. Visit `http://localhost:8081/neon-test`
3. Use the interface to:
   - Test database connection
   - Create sample tables
   - Insert and query data

### Programmatic Testing
```typescript
import { testNeonConnection } from '@/integrations/neon/test-connection';

const result = await testNeonConnection();
if (result.success) {
  console.log('Database connected successfully');
} else {
  console.error('Connection failed:', result.error);
}
```

## File Structure

```
src/integrations/neon/
├── client.ts           # Main database client
├── types.ts           # TypeScript type definitions
├── test-connection.ts # Testing utilities
└── index.ts          # Export barrel file
```

## Security Best Practices

### Environment Variables
- ✅ `.env` is in `.gitignore`
- ✅ Use `.env.example` for documentation
- ✅ Never commit actual credentials

### Connection Security
- ✅ SSL mode required
- ✅ Channel binding enabled
- ✅ Pooled connections for performance

## Migration Status

### Current State
- ✅ Neon database client configured
- ✅ Test utilities implemented
- ✅ Environment setup complete
- ⏳ Legacy Supabase code commented out (TODO)

### Migration TODO
The following components need migration from Supabase to Neon:

1. **FormContext.tsx** - Lead data persistence
2. **StepOne.tsx** - Email lookup functionality  
3. **Booking.tsx** - Completion status updates
4. **redirectionService.ts** - User existence checks

### Required Database Schema
When ready to migrate, create these tables:

```sql
-- Leads table for booking system
CREATE TABLE leads (
  id SERIAL PRIMARY KEY,
  first_name VARCHAR(255),
  last_name VARCHAR(255),
  email VARCHAR(255) UNIQUE NOT NULL,
  company_name VARCHAR(255),
  website VARCHAR(255),
  business_type VARCHAR(100),
  funding_status VARCHAR(100),
  business_stage VARCHAR(100),
  industry VARCHAR(255),
  other_industry TEXT,
  running_ads VARCHAR(50),
  ads_effective VARCHAR(50),
  ad_budget VARCHAR(100),
  marketing_management VARCHAR(100),
  decision_makers TEXT[],
  implementation_timeline VARCHAR(100),
  marketing_areas TEXT[],
  marketing_challenges TEXT,
  session_outcomes TEXT,
  materials_to_share TEXT[],
  other_materials TEXT,
  additional_info TEXT,
  current_step INTEGER DEFAULT 1,
  is_completed BOOLEAN DEFAULT FALSE,
  converted_to_customer BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for performance
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_created_at ON leads(created_at);
```

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check if your IP is whitelisted in Neon dashboard
   - Verify connection string format

2. **SSL Errors**
   - Ensure `sslmode=require` is in connection string
   - Check if `channel_binding=require` is included

3. **Environment Variables Not Loading**
   - Restart development server after changing `.env`
   - Verify file is named exactly `.env` (not `.env.txt`)

### Debug Commands
```bash
# Test connection from command line
psql "postgresql://user:<EMAIL>/db?sslmode=require"

# Check environment variables
echo $DATABASE_URL
```

## Resources

- [Neon Documentation](https://neon.tech/docs)
- [Neon Serverless Driver](https://github.com/neondatabase/serverless)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## Support

For database-related issues:
1. Check the test interface at `/neon-test`
2. Review console logs for error details
3. Verify environment configuration
4. Consult Neon dashboard for connection status
