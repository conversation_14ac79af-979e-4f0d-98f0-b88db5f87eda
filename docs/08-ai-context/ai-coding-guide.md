# AI-Assisted Development Guide for Asger.me

## 🤖 Project Context for AI Tools

### Quick Copy-Paste Context

When starting a new conversation with an AI coding assistant, use this context:

```
I'm building a personal freelance website (asger.me) using:
- React 18.3.1 + TypeScript + Vite
- Tailwind CSS with custom neo-brutalist design system
- Shadcn UI components
- Bold 3px black borders, 4px shadow offsets
- Playful animations and interactions
- Mobile-first responsive design

The site showcases my services (marketing consulting, public speaking, workshops), includes a blog/content section, and features Stripe payment integration. 

Key design classes:
- neo-card: border-[3px] border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]
- neo-button: Same border/shadow plus active state animations
- hover-lift: Lifts element on hover with increased shadow
- Primary color: #FFD700 (gold)
- Secondary: #00A3FF (blue)
- Accent: #FF3366 (pink)
```

## 📋 Effective Prompt Templates

### 1. Creating New Components

```
I need a [ComponentName] component for asger.me that [describe functionality].

Requirements:
- Must follow neo-brutalist design (3px black borders, 4px shadows)
- Use these colors: primary (#FFD700), secondary (#00A3FF), accent (#FF3366)
- Include hover effects (hover-lift or hover-tilt)
- Mobile-first responsive design
- TypeScript with proper interfaces
- Follow this structure:

interface ComponentProps {
  // define props
}

const ComponentName: React.FC<ComponentProps> = ({ props }) => {
  // component logic
}

Existing similar components use patterns like:
[paste relevant code example]
```

### 2. Adding Features

```
I want to add [feature name] to asger.me.

Current state:
- [Describe what exists]
- [Relevant files/components]

Desired outcome:
- [Specific functionality needed]
- [User interaction flow]
- [Visual requirements]

Technical constraints:
- Must work with existing FormContext for forms
- Use Tanstack Query for data fetching
- Integrate with existing neo-brutalist design system
- Follow mobile-first approach

Similar features in the codebase:
[paste example code]
```

### 3. Debugging Issues

```
I'm having an issue in asger.me:

What's happening:
- [Current behavior]
- [Error messages if any]

Expected behavior:
- [What should happen]

Relevant code:
```tsx
[paste the problematic code]
```

Context:

- Using React 18.3.1 with TypeScript
- [Relevant libraries involved]
- [Any recent changes made]

What I've tried:

- [Debugging steps taken]

```

### 4. Refactoring Code

```

I need to refactor this code in asger.me to be more maintainable:

Current code:

```tsx
[paste current implementation]
```

Issues with current approach:

- [List problems]

Requirements for refactored version:

- Maintain neo-brutalist styling
- Improve TypeScript types
- Better error handling
- Follow project patterns like:
  - FormContext for multi-step forms
  - Service layer for API calls
  - Consistent loading/error states

```

## 🎨 Design System Quick Reference

### Colors
```tsx
// Tailwind classes
primary: 'bg-primary' // #FFD700 - Gold
secondary: 'bg-secondary' // #00A3FF - Blue  
accent: 'bg-accent' // #FF3366 - Pink
background: 'bg-background' // #F8F8F8
foreground: 'text-foreground' // #1A1A1A
```

### Standard Component Structure

```tsx
// 1. Imports
import React, { useState, useEffect } from 'react'
import { LucideIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

// 2. Types
interface ComponentProps {
  title: string
  description?: string
  icon?: LucideIcon
  color?: 'primary' | 'secondary' | 'accent'
  onClick?: () => void
}

// 3. Component
const ComponentName: React.FC<ComponentProps> = ({
  title,
  description,
  icon: Icon,
  color = 'primary',
  onClick
}) => {
  // 4. State
  const [isHovered, setIsHovered] = useState(false)
  
  // 5. Handlers
  const handleClick = () => {
    onClick?.()
  }
  
  // 6. Render
  return (
    <div 
      className={cn(
        'neo-card p-6 cursor-pointer transition-all duration-300',
        'hover-lift',
        color === 'primary' && 'bg-primary',
        color === 'secondary' && 'bg-secondary',
        color === 'accent' && 'bg-accent'
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {Icon && <Icon className="w-8 h-8 mb-4" />}
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      {description && (
        <p className="text-sm opacity-90">{description}</p>
      )}
    </div>
  )
}

export default ComponentName
```

### Animation Classes

```css
/* Slide in from left */
.animate-slide-in

/* Fade in */
.animate-fade-in

/* Float up and down */
.animate-float

/* Bounce lightly */
.animate-bounce-light

/* Wiggle for attention */
.animate-wiggle

/* Hover effects */
.hover-lift /* Lifts with shadow */
.hover-tilt /* Slight rotation */
.hover-glow /* Glowing shadow */
```

## 📁 File Organization

When AI creates new files, follow this structure:

```
src/
├── components/
│   ├── [feature]/          # Feature-specific components
│   │   ├── ComponentName.tsx
│   │   └── index.ts
│   ├── ui/                # Shared UI components
│   └── layout/            # Layout components
├── pages/                 # Route pages
├── services/              # API and business logic
├── hooks/                 # Custom React hooks
├── contexts/              # React contexts
├── lib/                   # Utilities
└── types/                 # TypeScript type definitions
```

## 🧪 Component Testing Checklist

When creating components with AI, ensure:

- [ ] **TypeScript**: All props have interfaces
- [ ] **Responsive**: Works on mobile (320px) to desktop
- [ ] **Styling**: Uses neo-brutalist design system
- [ ] **Interactions**: Hover, focus, active states defined
- [ ] **Loading**: Shows loading state for async operations
- [ ] **Errors**: Handles and displays errors gracefully
- [ ] **Accessibility**: ARIA labels, keyboard navigation
- [ ] **Animation**: Smooth transitions, no janky movements

## 💡 Common Patterns

### Form Handling

```tsx
const [formData, setFormData] = useState<FormType>(initial)
const [errors, setErrors] = useState<Record<string, string>>({})
const [isLoading, setIsLoading] = useState(false)

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault()
  setIsLoading(true)
  try {
    // Submit logic
  } catch (error) {
    // Error handling
  } finally {
    setIsLoading(false)
  }
}
```

### Service Card Pattern

```tsx
interface ServiceCardProps {
  service: {
    id: string
    title: string
    description: string
    price: number | 'custom'
    icon: LucideIcon
    color: string
  }
  onSelect: (id: string) => void
}
```

### API Integration Pattern

```tsx
const fetchData = async (): Promise<DataType> => {
  try {
    const response = await fetch('/api/endpoint')
    if (!response.ok) throw new Error('Failed to fetch')
    return await response.json()
  } catch (error) {
    console.error('API Error:', error)
    throw error
  }
}
```

## 🚨 Important Reminders

### Always Include

1. Error boundaries for component trees
2. Loading states for async operations
3. Mobile-first responsive design
4. Proper TypeScript types
5. Accessibility attributes

### Never Forget

1. The neo-brutalist aesthetic is key to the brand
2. Animations should be playful but not distracting
3. Performance matters - lazy load when needed
4. Test on real mobile devices
5. Keep the personality in the design

## 🔧 Troubleshooting Common Issues

### Issue: Styling not applying

**Solution**: Check for Tailwind class conflicts, ensure classes are in correct order

### Issue: TypeScript errors

**Solution**: Define interfaces for all props, use proper type guards

### Issue: Animation janky

**Solution**: Use transform instead of position, add will-change property

### Issue: Mobile layout broken

**Solution**: Start with mobile design, use responsive utilities correctly

## 📚 Resources

### Internal Documentation

- [Design System](../03-design-system/neo-brutalist-system.md)
- [Component Library](../04-components/component-library.md)
- [Coding Standards](../07-development/coding-standards.md)

### External Resources

- [React Docs](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Shadcn UI](https://ui.shadcn.com)
- [Lucide Icons](https://lucide.dev)
