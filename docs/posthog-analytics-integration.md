# PostHog Analytics Integration

## Overview

This document describes the comprehensive PostHog analytics integration implemented in the Asger.me application. PostHog is used to track user behavior, form interactions, and business-critical events.

## Setup

### Environment Variables

The following environment variables are required in `.env`:

```env
VITE_PUBLIC_POSTHOG_KEY=phc_EuOuxgmXtDCr8PymEWpvtmMoa2FjIuK0yPr7CyEYzHu
VITE_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com
```

### Installation

PostHog is installed via npm:

```bash
npm install posthog-js
```

### Configuration

PostHog is initialized in `src/main.tsx` with the PostHogProvider wrapping the entire application:

```tsx
import { PostHogProvider } from 'posthog-js/react'

const options = {
  api_host: import.meta.env.VITE_PUBLIC_POSTHOG_HOST,
  person_profiles: 'identified_only',
}

<PostHogProvider apiKey={import.meta.env.VITE_PUBLIC_POSTHOG_KEY} options={options}>
  <App />
</PostHogProvider>
```

## Analytics Service

### Core Service (`src/services/analytics.ts`)

The analytics service provides:

- **Type-safe event tracking** with TypeScript interfaces
- **Centralized tracking logic** for consistency
- **User identification** for customer journey tracking
- **Error handling** and fallbacks
- **Utility functions** for common tracking patterns

### Key Features

1. **Typed Events**: All events are strongly typed with specific properties
2. **Automatic Metadata**: Events include timestamp, URL, referrer, and user agent
3. **User Identification**: Links events to specific users when contact info is provided
4. **Error Tracking**: Captures and tracks application errors

## Tracked Events

### Page Views

- **Event**: `page_viewed`
- **Trigger**: Automatic on route changes
- **Properties**: page_name, path, referrer
- **Implementation**: `PageViewTracker` component

### Booking Form Events

1. **Form Started**
   - **Event**: `booking_form_started`
   - **Trigger**: When form component mounts
   - **Properties**: source (referrer)

2. **Step Completed**
   - **Event**: `booking_form_step_completed`
   - **Trigger**: When user successfully moves to next step
   - **Properties**: step, step_name

3. **Step Abandoned**
   - **Event**: `booking_form_step_abandoned`
   - **Trigger**: When user goes back or leaves
   - **Properties**: step, step_name, time_spent_seconds

4. **Form Completed**
   - **Event**: `booking_form_completed`
   - **Trigger**: When form is successfully submitted
   - **Properties**: total_time_seconds, lead_id

5. **Form Errors**
   - **Event**: `booking_form_error`
   - **Trigger**: When validation fails or errors occur
   - **Properties**: step, error_type, error_message

### Contact Form Events

1. **Form Started**
   - **Event**: `contact_form_started`
   - **Trigger**: When user first interacts with form
   - **Properties**: source

2. **Form Submitted**
   - **Event**: `contact_form_submitted`
   - **Trigger**: When form is successfully submitted
   - **Properties**: has_company, message_length

3. **Form Errors**
   - **Event**: `contact_form_error`
   - **Trigger**: When submission fails
   - **Properties**: error_type, error_message

### Service Checkout Events

1. **Service Viewed**
   - **Event**: `service_viewed`
   - **Trigger**: When service detail page loads
   - **Properties**: service_id, service_name, service_price

2. **Checkout Started**
   - **Event**: `service_checkout_started`
   - **Trigger**: When user clicks "Buy Now" or "Request Quote"
   - **Properties**: service_id, service_name, service_price

3. **Checkout Completed**
   - **Event**: `service_checkout_completed`
   - **Trigger**: When payment is successful
   - **Properties**: service_id, service_name, service_price, payment_method

4. **Checkout Abandoned**
   - **Event**: `service_checkout_abandoned`
   - **Trigger**: When user leaves checkout flow
   - **Properties**: service_id, service_name, step

### User Interaction Events

1. **CTA Clicked**
   - **Event**: `cta_clicked`
   - **Trigger**: When any call-to-action button is clicked
   - **Properties**: cta_text, cta_location, destination

2. **Navigation Clicked**
   - **Event**: `navigation_clicked`
   - **Trigger**: When navigation links are clicked
   - **Properties**: link_text, destination, source_page

3. **Website Analyzed**
   - **Event**: `website_analyzed`
   - **Trigger**: When AI analyzes user's website in booking form
   - **Properties**: website_url, analysis_successful, company_detected, industry_detected

4. **External Redirect**
   - **Event**: `external_redirect`
   - **Trigger**: When user is redirected to external sites
   - **Properties**: destination, source_page, context

## User Identification

Users are identified when they provide contact information:

1. **Booking Form**: Identified with email, name, company, and business details
2. **Contact Form**: Identified with email, name, and company
3. **Service Checkout**: Identified with email and name

## Implementation Details

### Components with Tracking

- `src/components/analytics/PageViewTracker.tsx` - Automatic page view tracking
- `src/contexts/FormContext.tsx` - Booking form event tracking
- `src/pages/Contact.tsx` - Contact form tracking
- `src/components/services/ServiceSingleContent.tsx` - Service view tracking
- `src/components/checkout/PaymentForm.tsx` - Checkout tracking
- `src/components/Hero.tsx` - Hero CTA tracking
- `src/components/ContactSection.tsx` - Contact section CTA tracking
- `src/components/Header.tsx` - Header CTA tracking
- `src/components/Footer.tsx` - Footer CTA tracking
- `src/components/booking/StepOne.tsx` - Website analysis tracking

### Error Handling

The analytics service includes error handling to ensure tracking failures don't break the application:

- Console warnings for missing PostHog instance
- Try-catch blocks around tracking calls
- Fallback behavior when tracking fails

## Testing

To test the PostHog integration:

1. Open browser developer tools
2. Navigate through the application
3. Check the Network tab for requests to PostHog
4. Verify events in the PostHog dashboard
5. Test form submissions and interactions

## Best Practices

1. **Privacy**: Only track necessary data for business insights
2. **Performance**: Tracking is non-blocking and doesn't affect user experience
3. **Type Safety**: All events use TypeScript interfaces
4. **Consistency**: Use the centralized analytics service for all tracking
5. **Documentation**: Keep this document updated when adding new events

## Future Enhancements

Potential improvements to consider:

1. **A/B Testing**: Use PostHog's feature flags for experiments
2. **Cohort Analysis**: Track user segments and behavior patterns
3. **Funnel Analysis**: Set up conversion funnels for key user journeys
4. **Session Recording**: Enable session recordings for UX insights
5. **Custom Dashboards**: Create business-specific dashboards in PostHog
