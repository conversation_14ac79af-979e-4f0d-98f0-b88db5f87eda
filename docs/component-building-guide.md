# Component Building Guide

## Overview

This guide provides step-by-step instructions for building components in the Marketing Therapy Journey project, following our established neo-brutalist design system and React patterns.

## Quick Start Checklist

### Before Building a Component

- [ ] Review the design requirements and neo-brutalist styling needs
- [ ] Identify if it's a page, section, form, or UI component
- [ ] Check if similar components exist for pattern reference
- [ ] Define TypeScript interfaces for props and state
- [ ] Plan responsive behavior and animations

### Component Creation Steps

1. **Create the component file** in the appropriate directory
2. **Define TypeScript interfaces** for props and internal state
3. **Implement the component structure** following established patterns
4. **Apply neo-brutalist styling** using our design system classes
5. **Add responsive design** with Tailwind breakpoints
6. **Implement animations** if required
7. **Test functionality** and responsiveness
8. **Integrate with context/services** as needed

## Component Types & Patterns

### 1. Landing Page Section Components

#### Template Structure
```tsx
import React from 'react';

interface SectionProps {
  // Define props here
}

const SectionName: React.FC<SectionProps> = ({ ...props }) => {
  return (
    <section id="section-name" className="py-20 overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Section header */}
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
            Section Tag
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            Section Title
          </h2>
        </div>
        
        {/* Section content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Content items */}
        </div>
      </div>
    </section>
  );
};

export default SectionName;
```

#### Key Patterns
- **Container**: Always use `container mx-auto px-4`
- **Spacing**: Standard `py-20` for section padding
- **Headers**: Rotated tag + large title pattern
- **Grids**: Responsive grid layouts with gap-8

### 2. Form Components

#### Form Step Template
```tsx
import React from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Label } from '../ui/label';
import { Input } from '../ui/input';

const StepName = () => {
  const { formData, updateFormData, errors } = useFormContext();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-2">Step Title</h2>
      <p className="mb-6">Step description</p>
      
      <div className="space-y-4">
        {/* Form fields */}
        <div>
          <Label htmlFor="fieldName" className="block mb-1 font-medium">
            Field Label
          </Label>
          <Input 
            type="text"
            id="fieldName"
            name="fieldName"
            value={formData.fieldName}
            onChange={handleChange}
            className={`neo-input w-full ${errors.fieldName ? 'border-red-500' : ''}`}
          />
          {errors.fieldName && (
            <p className="mt-1 text-red-500 text-sm">{errors.fieldName}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StepName;
```

#### Form Field Patterns

##### Text Input
```tsx
<div>
  <Label htmlFor="fieldName" className="block mb-1 font-medium">
    Field Label
  </Label>
  <Input 
    type="text"
    id="fieldName"
    name="fieldName"
    value={formData.fieldName}
    onChange={handleChange}
    className={`neo-input w-full ${errors.fieldName ? 'border-red-500' : ''}`}
  />
  {errors.fieldName && (
    <p className="mt-1 text-red-500 text-sm">{errors.fieldName}</p>
  )}
</div>
```

##### Radio Group
```tsx
<div>
  <Label className="block mb-3 font-medium">
    Question Text
  </Label>
  <RadioGroup
    value={formData.fieldName}
    onValueChange={(value) => updateFormData({ fieldName: value })}
    className="space-y-2"
  >
    {options.map(option => (
      <div key={option.value} className="flex items-center space-x-2 neo-border p-3">
        <RadioGroupItem value={option.value} id={option.value} />
        <Label htmlFor={option.value}>{option.label}</Label>
      </div>
    ))}
  </RadioGroup>
  {errors.fieldName && (
    <p className="mt-1 text-red-500 text-sm">{errors.fieldName}</p>
  )}
</div>
```

##### Checkbox Group
```tsx
<div>
  <Label className="block mb-3 font-medium">
    Select all that apply:
  </Label>
  <div className="space-y-2">
    {options.map(option => (
      <div key={option.id} className="flex items-center space-x-2 neo-border p-3">
        <Checkbox
          id={option.id}
          checked={formData.fieldName.includes(option.id)}
          onCheckedChange={() => handleCheckboxChange('fieldName', option.id)}
        />
        <Label htmlFor={option.id}>{option.label}</Label>
      </div>
    ))}
  </div>
</div>
```

### 3. Card Components

#### Basic Card Pattern
```tsx
<div className="neo-card p-6 bg-white hover:-translate-y-2 cursor-pointer">
  <div className="mb-4 text-black">{icon}</div>
  <h3 className="text-xl font-bold mb-3">{title}</h3>
  <p>{description}</p>
</div>
```

#### Rotated Card with Color
```tsx
<div 
  className="neo-card p-6 bg-white relative"
  style={{ 
    backgroundColor: colors[index % colors.length],
    transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
  }}
>
  {/* Card content */}
</div>
```

## Styling Guidelines

### 1. Neo-Brutalist Classes

#### Core Classes
- `neo-border`: 3px black border
- `neo-shadow`: Offset drop shadow
- `neo-card`: Combined border + shadow + background
- `neo-button`: Interactive button styling
- `neo-input`: Form input styling

#### Usage Examples
```tsx
// Button
<button className="neo-button bg-primary hover:bg-primary/90 text-black">
  Click Me
</button>

// Card
<div className="neo-card p-6 bg-white">
  Content
</div>

// Input
<input className="neo-input w-full" />
```

### 2. Color Application

#### Primary Colors
```tsx
// Yellow (Primary)
className="bg-primary text-black"

// Blue (Secondary) 
className="bg-secondary text-white"

// Pink (Accent)
className="bg-accent text-white"
```

#### Dynamic Color Assignment
```tsx
const colors = ['#FFFFFF', '#FFD700', '#00A3FF', '#FF3366'];
const backgroundColor = colors[index % colors.length];
```

### 3. Responsive Design

#### Breakpoint Usage
```tsx
// Mobile-first approach
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
```

#### Common Responsive Patterns
- `text-xl md:text-2xl lg:text-3xl`: Responsive typography
- `p-4 md:p-6 lg:p-8`: Responsive padding
- `hidden md:block`: Show/hide on different screens

### 4. Animation Implementation

#### CSS Animations
```tsx
// Fade in animation
<div className="animate-fade-in">

// Slide in with delay
<div className="animate-slide-in [animation-delay:0.2s] opacity-0">

// Floating animation
<div className="animate-float">
```

#### Transform Rotations
```tsx
// Random rotation
style={{ transform: `rotate(${Math.random() * 6 - 3}deg)` }}

// Alternating rotation
style={{ transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)` }}
```

## State Management Patterns

### 1. Form Context Usage

#### Accessing Form State
```tsx
const { formData, updateFormData, errors, currentStep } = useFormContext();
```

#### Updating Form Data
```tsx
// Single field update
updateFormData({ fieldName: value });

// Multiple fields update
updateFormData({ 
  field1: value1, 
  field2: value2 
});
```

#### Error Handling
```tsx
// Check for errors
{errors.fieldName && (
  <p className="mt-1 text-red-500 text-sm">{errors.fieldName}</p>
)}

// Clear errors on input
const handleChange = (e) => {
  updateFormData({ [e.target.name]: e.target.value });
  // Errors are automatically cleared in context
};
```

### 2. Local State Patterns

#### Loading States
```tsx
const [isLoading, setIsLoading] = useState(false);

// Usage
{isLoading ? (
  <Loader2 className="animate-spin h-4 w-4" />
) : (
  <span>Content</span>
)}
```

#### Toggle States
```tsx
const [isOpen, setIsOpen] = useState(false);

// Usage
<button onClick={() => setIsOpen(!isOpen)}>
  Toggle
</button>
```

## Integration Patterns

### 1. Database Integration

#### Using Neon Client
```tsx
import { sql } from '@/integrations/neon/client';

const saveData = async (data) => {
  try {
    const result = await sql`
      INSERT INTO table_name (column1, column2)
      VALUES (${data.field1}, ${data.field2})
      RETURNING id
    `;
    return result[0].id;
  } catch (error) {
    console.error('Database error:', error);
    throw error;
  }
};
```

### 2. Service Integration

#### API Service Pattern
```tsx
import { apiService } from '@/services/apiService';

const fetchData = async () => {
  try {
    const response = await apiService.get('/endpoint');
    return response.data;
  } catch (error) {
    toast({
      title: "Error",
      description: "Failed to fetch data",
      variant: "destructive"
    });
  }
};
```

### 3. Toast Notifications

#### Success Toast
```tsx
toast({
  title: "Success",
  description: "Operation completed successfully",
});
```

#### Error Toast
```tsx
toast({
  title: "Error", 
  description: "Something went wrong",
  variant: "destructive"
});
```

## Testing Guidelines

### 1. Component Testing

#### Basic Test Structure
```tsx
import { render, screen } from '@testing-library/react';
import ComponentName from './ComponentName';

describe('ComponentName', () => {
  it('renders correctly', () => {
    render(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### 2. Form Testing

#### Form Interaction Testing
```tsx
import { fireEvent } from '@testing-library/react';

it('updates form data on input change', () => {
  render(<FormComponent />);
  const input = screen.getByLabelText('Field Label');
  fireEvent.change(input, { target: { value: 'test value' } });
  expect(input.value).toBe('test value');
});
```

## Common Pitfalls & Solutions

### 1. Styling Issues

#### Problem: Neo-border not showing
**Solution**: Ensure parent has proper background color
```tsx
// Wrong
<div className="neo-border">Content</div>

// Correct  
<div className="neo-border bg-white">Content</div>
```

#### Problem: Animations not working
**Solution**: Check for opacity-0 initial state
```tsx
<div className="animate-slide-in [animation-delay:0.1s] opacity-0">
```

### 2. Form Issues

#### Problem: Form data not updating
**Solution**: Verify field names match FormData interface
```tsx
// Ensure name attribute matches interface
<input name="firstName" /> // Must match FormData.firstName
```

#### Problem: Validation not working
**Solution**: Check validation rules in FormContext
```tsx
// Validation should be defined in validateCurrentStep()
```

## Performance Best Practices

### 1. Component Optimization

- Use React.memo for expensive components
- Implement proper key props for lists
- Avoid inline object creation in render

### 2. Bundle Optimization

- Import only needed utilities
- Use dynamic imports for large components
- Optimize image loading with proper sizing

### 3. Animation Performance

- Use transform instead of changing layout properties
- Implement will-change for animated elements
- Use CSS animations over JavaScript when possible

## Deployment Considerations

### 1. Build Optimization

- Verify all imports are correct
- Check for unused dependencies
- Test responsive design on multiple devices

### 2. Accessibility

- Add proper ARIA labels
- Ensure keyboard navigation works
- Test with screen readers
- Maintain proper color contrast

### 3. Browser Compatibility

- Test on major browsers
- Verify CSS Grid/Flexbox support
- Check animation performance on lower-end devices
