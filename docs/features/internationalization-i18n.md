# Internationalization (i18n) Feature

## Overview

This document outlines the implementation plan for adding multi-language support to the Marketing Therapy Journey application, with Danish (da) as the primary language and English (en) as the secondary language.

## Recommended Solution: react-i18next

After comprehensive research, **react-i18next** is the recommended internationalization library for this project.

### Why react-i18next?

- **Danish Language Support**: Excellent built-in support for Danish formatting conventions
- **TypeScript Integration**: Full type-safe translation keys with autocompletion
- **Performance**: Lazy loading and namespace-based code splitting
- **Ecosystem**: Largest community (2M+ weekly downloads) with extensive plugins
- **Vite Compatibility**: Seamless integration with our existing build system

## Danish Language Requirements

### Special Considerations

1. **Character Encoding**: Full UTF-8 support for Danish characters (æ, ø, å)
2. **Date Format**: DD/MM/YYYY (e.g., 25/12/2024)
3. **Number Format**: Comma as decimal separator (e.g., 1.234,56)
4. **Currency**: Danish Krone (DKK)
5. **Pluralization**: Simple rules similar to English

## Implementation Architecture

### File Structure

```
src/
├── i18n/
│   ├── config.ts              # Main i18n configuration
│   ├── resources/
│   │   ├── da/               # Danish translations (primary)
│   │   │   ├── common.json   # Shared UI elements
│   │   │   ├── booking.json  # Booking flow translations
│   │   │   ├── dashboard.json # Dashboard translations
│   │   │   └── landing.json  # Landing page translations
│   │   └── en/               # English translations
│   │       ├── common.json
│   │       ├── booking.json
│   │       ├── dashboard.json
│   │       └── landing.json
│   └── types/
│       └── i18next.d.ts      # TypeScript definitions
└── components/
    └── LanguageSwitcher.tsx  # Language selection component
```

### Key Features to Implement

1. **Automatic Language Detection**
   - Detect browser language
   - Fall back to Danish if unsupported language
   - Store preference in localStorage

2. **TypeScript Type Safety**
   - Auto-generated types from translation files
   - IDE autocompletion for translation keys
   - Compile-time error checking

3. **Performance Optimization**
   - Lazy load translation namespaces
   - Bundle splitting by language
   - Cache translations

4. **Developer Experience**
   - Hot module replacement for translations
   - Missing translation warnings
   - Extraction tools for hardcoded strings

## Installation Steps

### 1. Install Dependencies

```bash
npm install react-i18next i18next i18next-browser-languagedetector i18next-http-backend
npm install --save-dev @types/react-i18next
```

### 2. Basic Configuration

```typescript
// src/i18n/config.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpApi from 'i18next-http-backend';

i18n
  .use(HttpApi)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    lng: 'da',              // Default to Danish
    fallbackLng: 'en',
    debug: import.meta.env.DEV,
    
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
    
    interpolation: {
      escapeValue: false,   // React already escapes
    },
    
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    
    ns: ['common', 'booking', 'dashboard', 'landing'],
    defaultNS: 'common',
  });

export default i18n;
```

### 3. TypeScript Configuration

```typescript
// src/i18n/types/i18next.d.ts
import 'i18next';
import type common from '../resources/da/common.json';
import type booking from '../resources/da/booking.json';
import type dashboard from '../resources/da/dashboard.json';
import type landing from '../resources/da/landing.json';

declare module 'i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: {
      common: typeof common;
      booking: typeof booking;
      dashboard: typeof dashboard;
      landing: typeof landing;
    };
  }
}
```

### 4. Main App Integration

```typescript
// src/main.tsx
import './i18n/config';

// Rest of your app initialization
```

## Translation Examples

### Danish (Primary) - `da/booking.json`

```json
{
  "title": "Book din gratis konsultation",
  "steps": {
    "websiteInfo": "Hjemmeside information",
    "businessGoals": "Forretningsmål",
    "targetAudience": "Målgruppe",
    "contactDetails": "Kontaktoplysninger"
  },
  "form": {
    "websiteUrl": "Hjemmeside URL",
    "websitePlaceholder": "https://din-hjemmeside.dk",
    "businessName": "Virksomhedsnavn",
    "primaryGoal": "Hvad er dit primære mål?",
    "submit": "Book konsultation",
    "back": "Tilbage",
    "next": "Næste"
  },
  "validation": {
    "required": "Dette felt er påkrævet",
    "invalidUrl": "Indtast en gyldig URL",
    "invalidEmail": "Indtast en gyldig e-mailadresse"
  }
}
```

### English (Secondary) - `en/booking.json`

```json
{
  "title": "Book Your Free Consultation",
  "steps": {
    "websiteInfo": "Website Information",
    "businessGoals": "Business Goals",
    "targetAudience": "Target Audience",
    "contactDetails": "Contact Details"
  },
  "form": {
    "websiteUrl": "Website URL",
    "websitePlaceholder": "https://your-website.com",
    "businessName": "Business Name",
    "primaryGoal": "What is your primary goal?",
    "submit": "Book Consultation",
    "back": "Back",
    "next": "Next"
  },
  "validation": {
    "required": "This field is required",
    "invalidUrl": "Please enter a valid URL",
    "invalidEmail": "Please enter a valid email address"
  }
}
```

## Component Usage

### Basic Translation Hook

```typescript
import { useTranslation } from 'react-i18next';

function BookingForm() {
  const { t } = useTranslation('booking');
  
  return (
    <form>
      <h1>{t('title')}</h1>
      <Input
        label={t('form.websiteUrl')}
        placeholder={t('form.websitePlaceholder')}
      />
      <Button>{t('form.submit')}</Button>
    </form>
  );
}
```

### Language Switcher Component

```typescript
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';

export function LanguageSwitcher() {
  const { i18n } = useTranslation();
  
  const toggleLanguage = () => {
    const newLang = i18n.language === 'da' ? 'en' : 'da';
    i18n.changeLanguage(newLang);
  };
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
    >
      {i18n.language === 'da' ? 'EN' : 'DA'}
    </Button>
  );
}
```

### Date and Number Formatting

```typescript
import { useTranslation } from 'react-i18next';

function PriceDisplay({ amount }: { amount: number }) {
  const { i18n } = useTranslation();
  
  const formattedPrice = new Intl.NumberFormat(
    i18n.language === 'da' ? 'da-DK' : 'en-US',
    {
      style: 'currency',
      currency: i18n.language === 'da' ? 'DKK' : 'USD',
    }
  ).format(amount);
  
  return <span>{formattedPrice}</span>;
}
```

## Migration Checklist

- [ ] Install i18n dependencies
- [ ] Set up i18n configuration with Danish as default
- [ ] Create translation file structure
- [ ] Configure TypeScript types
- [ ] Integrate with main app
- [ ] Create language switcher component
- [ ] Extract existing hardcoded strings
- [ ] Translate all content to Danish
- [ ] Test language switching functionality
- [ ] Implement date/number formatting
- [ ] Add language preference persistence
- [ ] Update component documentation

## Performance Considerations

1. **Bundle Size Impact**: ~22KB added (can be reduced with lazy loading)
2. **Code Splitting**: Separate bundles for each language
3. **Lazy Loading**: Load translations on-demand by namespace
4. **Caching**: Translations cached in localStorage

## SEO Implications

1. Add `lang` attribute to HTML: `<html lang="da">`
2. Implement hreflang tags for language alternatives
3. Consider URL structure: `/da/booking` vs `/en/booking`
4. Ensure meta descriptions are translated

## Future Enhancements

1. **Additional Languages**: Easy to add Swedish, Norwegian, German
2. **Translation Management**: Integration with translation services
3. **A/B Testing**: Test different translations for conversion
4. **Regional Variations**: Support for regional Danish variations
5. **RTL Support**: Not needed for current languages but available

## Resources

- [react-i18next Documentation](https://react.i18next.com/)
- [i18next Documentation](https://www.i18next.com/)
- [Danish Language Code Reference](https://www.loc.gov/standards/iso639-2/php/langcodes_name.php?iso_639_1=da)
- [Intl API Documentation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)