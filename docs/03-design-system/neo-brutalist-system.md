# Neo-Brutalist Design System for Asger.me

## 🎨 Design Philosophy

Neo-brutalism in web design takes inspiration from the Brutalist architectural movement, emphasizing raw, bold, and functional aesthetics. For asger.me, we're implementing a **"Playful Neo-Brutalism"** that combines:

- **Bold geometric shapes** with thick borders
- **Stark contrasts** and solid colors
- **Asymmetric layouts** with intentional imperfection
- **Playful interactions** that surprise and delight
- **Raw, honest design** that stands out from typical corporate sites

## 🎯 Core Design Principles

### 1. Be Bold, Not Boring

Every element should make a statement. No subtle gradients or thin borders here.

### 2. Embrace Imperfection

Slight rotations, asymmetric layouts, and "hand-drawn" feels make it human.

### 3. Function Meets Fun

While playful, every design choice must serve the user's needs.

### 4. High Contrast Accessibility

Bold doesn't mean inaccessible. Maintain WCAG AA standards.

## 🌈 Color System

### Primary Palette

```scss
// Core Colors
$primary: #FFC107;      // Amber - Main CTA, highlights
$secondary: #2196F3;    // Blue - Secondary actions, links
$accent: #FF3366;       // Pink - Alerts, new items, emphasis

// Neutral Colors
$black: #000000;        // Borders, text
$white: #FFFFFF;        // Backgrounds, cards
$gray-50: #FAFAFA;      // Subtle backgrounds
$gray-100: #F5F5F5;     // Alternate backgrounds
$gray-600: #525252;     // Muted text
$gray-900: #171717;     // Dark text

// Semantic Colors
$success: #10B981;      // Green - Success states
$warning: #F59E0B;      // Orange - Warnings
$error: #EF4444;        // Red - Errors
```

### Color Usage Guidelines

#### Primary (Amber) - #FFC107

- Main CTA buttons
- Important highlights
- Active states
- "New" badges
- Hero section decorative elements

#### Secondary (Blue) - #2196F3

- Secondary buttons
- Links and navigation
- Information panels
- Form focuses
- Supporting decorative elements

#### Accent (Pink) - #FF3366

- Special offers
- Notification badges
- Hover states for special elements
- "Book Now" emphasis
- Attention-grabbing decorative elements

### Color Combinations

```tsx
// High contrast combinations
'bg-primary text-black'      // Gold bg, black text
'bg-secondary text-white'    // Blue bg, white text
'bg-accent text-white'       // Pink bg, white text
'bg-black text-white'        // Black bg, white text
'bg-white text-black'        // White bg, black text

// Avoid these combinations
// ❌ 'bg-primary text-white' - Poor contrast
// ❌ 'bg-gray-100 text-gray-600' - Too subtle for neo-brutalist
```

## 🔲 Border & Shadow System

### Border Styles

```scss
// Standard border
.neo-border {
  border: 3px solid black;
}

// Utility classes
.border-[3px] // Thickness
.border-black // Color
.border-solid // Style

// Directional borders
.border-t-[3px] // Top only
.border-r-[3px] // Right only
.border-b-[3px] // Bottom only
.border-l-[3px] // Left only
```

### Shadow System

```scss
// Standard shadow (4px offset)
.neo-shadow {
  box-shadow: 4px 4px 0px 0px rgba(0,0,0,1);
}

// Shadow variations
.shadow-sm: 2px 2px 0px 0px rgba(0,0,0,1)    // Small
.shadow-md: 4px 4px 0px 0px rgba(0,0,0,1)    // Medium (default)
.shadow-lg: 6px 6px 0px 0px rgba(0,0,0,1)    // Large
.shadow-xl: 8px 8px 0px 0px rgba(0,0,0,1)    // Extra large

// Colored shadows
.shadow-primary: 4px 4px 0px 0px #FFC107
.shadow-secondary: 4px 4px 0px 0px #2196F3
.shadow-accent: 4px 4px 0px 0px #FF3366
```

### Interactive Shadow States

```scss
// Default state
.neo-shadow: 4px 4px 0px 0px rgba(0,0,0,1)

// Hover - Shadow grows (enhanced depth)
.hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]

// Active - Shadow shrinks with translation (pressed effect)
.active:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]
.active:translate-x-[2px]
.active:translate-y-[2px]

// Focus - Maintains hover state
.focus:shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]
```

#### Shadow Interaction Hierarchy

1. **Resting**: 4px offset - element appears elevated
2. **Hover**: 8px offset - element appears to lift higher
3. **Active**: 2px offset + translation - element appears pressed down
4. **Focus**: 8px offset - maintains hover appearance for accessibility

## 🎭 Component Patterns

### Neo-Card

```tsx
<div className="neo-card p-6 bg-white">
  {/* Card content */}
</div>

// With rotation for playfulness
<div className="neo-card p-6 bg-white rotate-1 hover:rotate-0 transition-transform">
  {/* Card content */}
</div>

// Colored variant
<div className="neo-card p-6 bg-primary">
  {/* Card content */}
</div>
```

### Neo-Button

The `.neo-button` class includes enhanced hover effects with shadow expansion:

```scss
.neo-button {
  @apply neo-border neo-shadow px-6 py-3 font-bold transition-all duration-200;

  // Hover state - shadow expands for depth
  &:hover {
    box-shadow: 8px 8px 0px 0px rgba(0,0,0,1);
  }

  // Active state - pressed effect
  &:active {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0px 0px rgba(0,0,0,1);
  }
}
```

```tsx
// Primary button
<button className="neo-button bg-primary hover:bg-primary/90 text-black">
  Book Consultation
</button>

// Secondary button
<button className="neo-button bg-secondary hover:bg-secondary/90 text-white">
  Learn More
</button>

// Outline button
<button className="neo-button bg-white hover:bg-gray-50 text-black">
  Cancel
</button>

// Icon button
<button className="neo-button bg-accent hover:bg-accent/90 text-white p-3">
  <Heart className="w-5 h-5" />
</button>
```

#### Button Interaction States

1. **Default**: 4px shadow, normal position
2. **Hover**: 8px shadow (enhanced depth effect)
3. **Active**: 2px shadow with button translation (pressed effect)
4. **Focus**: Maintains hover state with focus ring

### Neo-Input

```tsx
// Text input
<input 
  type="text"
  className="neo-input w-full"
  placeholder="Your name"
/>

// Textarea
<textarea 
  className="neo-input w-full h-32"
  placeholder="Tell me about your project"
/>

// Select
<select className="neo-input w-full">
  <option>Choose a service</option>
</select>
```

## 🎪 Animation System

### Core Animations

```scss
// Entrance animations
@keyframes slide-in {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

// Continuous animations
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(2deg); }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(1deg); }
  75% { transform: rotate(-1deg); }
}

// Interaction animations
@keyframes bounce-light {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}
```

### Animation Classes

```tsx
// Apply animations
'animate-slide-in'      // Slide from left on load
'animate-fade-in'       // Fade in on load
'animate-float'         // Continuous floating
'animate-bounce-light'  // Light bouncing
'animate-wiggle'        // Wiggle for attention

// With delays
'animate-slide-in [animation-delay:0.1s]'
'animate-slide-in [animation-delay:0.2s]'
'animate-slide-in [animation-delay:0.3s]'
```

### Hover Effects

```scss
// Lift effect
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-8px) rotate(2deg);
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,1);
  }
}

// Tilt effect
.hover-tilt {
  transition: all 0.3s ease;
  
  &:hover {
    transform: rotate(3deg) scale(1.02);
  }
}

// Glow effect (using current primary color)
.hover-glow {
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.6),
                4px 4px 0px 0px rgba(0,0,0,1);
  }
}

// Enhanced button hover (matches neo-button class)
.hover-button-enhance {
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 8px 8px 0px 0px rgba(0,0,0,1);
  }

  &:active {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0px 0px rgba(0,0,0,1);
  }
}
```

## 📐 Layout Patterns

### Asymmetric Grids

```tsx
// Staggered cards
<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
  <div className="neo-card rotate-1">Card 1</div>
  <div className="neo-card -rotate-2 md:mt-8">Card 2</div>
  <div className="neo-card rotate-1 md:mt-16">Card 3</div>
</div>
```

### Overlapping Elements

```tsx
// Overlapping decoration
<div className="relative">
  <div className="absolute -top-4 -right-4 w-20 h-20 bg-accent neo-border rotate-12 z-10" />
  <div className="neo-card relative z-20">
    Main content
  </div>
</div>
```

### Rotated Text Blocks

```tsx
// Rotated header
<h2 className="text-4xl font-bold -rotate-2 inline-block">
  My Services
</h2>

// Rotated badge
<span className="inline-block px-4 py-2 neo-border bg-primary -rotate-1">
  NEW
</span>
```

## 🎯 Interactive Elements

### Status Indicators

```tsx
// Online status dot
<div className="w-4 h-4 rounded-full bg-green-500 border-2 border-white shadow-[0_0_0_2px_black]" />

// Notification badge
<div className="absolute -top-2 -right-2 w-6 h-6 bg-accent rounded-full flex items-center justify-center text-white text-xs font-bold border-2 border-black">
  3
</div>
```

### Loading States

```tsx
// Neo-brutalist spinner
<div className="w-12 h-12 neo-border bg-primary animate-spin" />

// Progress bar
<div className="w-full h-8 neo-border bg-white">
  <div className="h-full bg-primary transition-all duration-300" style={{ width: '60%' }} />
</div>
```

### Tooltips

```tsx
// Neo-tooltip
<div className="relative group">
  <button>Hover me</button>
  <div className="absolute -top-12 left-1/2 -translate-x-1/2 neo-border neo-shadow bg-black text-white px-3 py-1 text-sm opacity-0 group-hover:opacity-100 transition-opacity">
    Tooltip text
  </div>
</div>
```

## 📱 Responsive Considerations

### Mobile Adaptations

- Reduce rotations on small screens (too chaotic)
- Maintain thick borders but consider 2px on mobile
- Stack overlapping elements vertically
- Ensure touch targets are 44px minimum

### Breakpoint Behaviors

```tsx
// Responsive rotation
'rotate-0 md:rotate-1'

// Responsive shadows
'shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] md:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]'

// Responsive borders
'border-2 md:border-[3px]'

// Responsive spacing
'p-4 md:p-6 lg:p-8'
```

## ✅ Do's and Don'ts

### Do's ✅

- Use thick, solid borders
- Apply bold shadows with hard edges
- Rotate elements slightly for playfulness
- Use high contrast color combinations
- Make interactive elements obvious
- Add unexpected hover effects

### Don'ts ❌

- Don't use gradients or transparency
- Don't use thin borders or subtle shadows
- Don't make everything perfectly aligned
- Don't use low contrast combinations
- Don't overdo animations (keep it smooth)
- Don't sacrifice usability for style

## 🧩 Component Examples

### Service Card

```tsx
<div className="neo-card p-6 bg-white hover-lift cursor-pointer group">
  <div className="w-12 h-12 neo-border bg-primary mb-4 flex items-center justify-center -rotate-3 group-hover:rotate-0 transition-transform">
    <Rocket className="w-6 h-6" />
  </div>
  <h3 className="text-xl font-bold mb-2">Marketing Strategy</h3>
  <p className="text-gray-600 mb-4">
    Transform your marketing with data-driven strategies
  </p>
  <div className="flex items-center justify-between">
    <span className="text-2xl font-bold">$500</span>
    <ArrowRight className="w-5 h-5 group-hover:translate-x-2 transition-transform" />
  </div>
</div>
```

### Call-to-Action Section

```tsx
<section className="relative py-20 overflow-hidden">
  {/* Background decorations */}
  <div className="absolute top-10 left-10 w-32 h-32 bg-accent neo-border rotate-12 opacity-20" />
  <div className="absolute bottom-10 right-10 w-40 h-40 bg-primary neo-border -rotate-6 opacity-20" />
  
  <div className="container mx-auto px-4 relative z-10">
    <div className="neo-card p-8 md:p-12 bg-primary text-center max-w-2xl mx-auto -rotate-1">
      <h2 className="text-3xl md:text-4xl font-bold mb-4">
        Ready to Transform Your Marketing?
      </h2>
      <p className="text-xl mb-8">
        Let's discuss how I can help you achieve your goals
      </p>
      <button className="neo-button bg-black text-white hover:bg-gray-900 text-lg px-8 py-4">
        Book Your Free Consultation
      </button>
    </div>
  </div>
</section>
```

## 🎯 Current Implementation Status

### ✅ Implemented Features

#### Color System
- **Primary**: #FFC107 (Amber) - Used for main CTAs and highlights
- **Secondary**: #2196F3 (Blue) - Used for secondary actions and links
- **Accent**: #FF3366 (Pink) - Used for special emphasis and decorative elements
- All colors maintain WCAG AA accessibility standards

#### Interactive Elements
- **Enhanced Button Hovers**: All `.neo-button` elements now feature 8px shadow expansion on hover
- **Interactive Logo**: Features dynamic status indicators and enhanced hover effects
- **Consistent Shadow Hierarchy**: 4px default → 8px hover → 2px active states

#### Typography & Layout
- **Space Grotesk**: Primary font for headings and UI elements
- **Inter**: Secondary font for body text and detailed content
- **Asymmetric Layouts**: Intentional rotations and imperfect alignments
- **Mobile-First**: Responsive design with appropriate mobile adaptations

### 🔄 Recent Updates (Latest)

1. **Button Hover Enhancement**: Added 8px shadow expansion matching interactive logo style
2. **Color Refinement**: Reverted accent color to preferred pink (#FF3366)
3. **Documentation Sync**: Updated all design system documentation to reflect current implementation

### 🎨 Design Consistency Guidelines

#### For Developers
```tsx
// Always use these classes for consistency
'neo-button'     // For all interactive buttons
'neo-card'       // For content containers
'neo-border'     // For consistent 3px black borders
'neo-shadow'     // For standard 4px shadows
'neo-input'      // For form elements

// Color combinations that work
'bg-primary text-black'      // High contrast, accessible
'bg-secondary text-white'    // Professional, readable
'bg-accent text-white'       // Attention-grabbing
'bg-white text-black'        // Clean, minimal
```

#### For Designers
- Maintain 3px border thickness for desktop, 2px for mobile
- Use 4px → 8px → 2px shadow progression for interactions
- Apply slight rotations (-2° to +2°) for playful elements
- Ensure all text maintains minimum 4.5:1 contrast ratio

### 🚀 Future Enhancements

#### Planned Improvements
- [ ] Dark mode variant with inverted color scheme
- [ ] Additional animation presets for micro-interactions
- [ ] Enhanced loading states with neo-brutalist styling
- [ ] Expanded color palette for semantic states

#### Component Roadmap
- [ ] Neo-brutalist form validation states
- [ ] Enhanced tooltip system
- [ ] Modal and overlay components
- [ ] Advanced grid layout patterns

This design system creates a memorable, engaging experience that reflects personality while maintaining professionalism and usability.
