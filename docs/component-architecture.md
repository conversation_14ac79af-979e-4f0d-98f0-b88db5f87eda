# Component Architecture & Design System Documentation

## Overview

This project follows a modern React architecture with TypeScript, using a neomorphic design system and component-based structure. The application is built with Vite, React Router, and Tailwind CSS, featuring a distinctive "neo-brutalist" aesthetic.

## Design System

### Visual Identity

The project uses a **Neo-Brutalist** design approach characterized by:
- Bold, thick black borders (3px)
- Drop shadows with offset positioning
- Bright, contrasting colors (yellow, pink, blue)
- Playful rotations and animations
- Clean typography with Space Grotesk and Inter fonts

### Color Palette

```css
/* Primary Colors */
--primary: 47 100% 50%;      /* Bright Yellow (#FFD700) */
--secondary: 196 100% 50%;   /* Bright Blue (#00A3FF) */
--accent: 339 100% 60%;      /* Bright Pink (#FF3366) */

/* Neutral Colors */
--background: 0 0% 98%;      /* Off-white */
--foreground: 0 0% 10%;      /* Near-black */
--border: 0 0% 0%;           /* Pure black */
```

### Typography

- **Primary Font**: Space Grotesk (headings, UI elements)
- **Secondary Font**: Inter (body text, forms)
- **Font Weights**: 300, 400, 500, 600, 700, 800

## Core Design Components

### 1. Neo-Brutalist Elements

#### Neo Border
```css
.neo-border {
  @apply border-[3px] border-black;
}
```

#### Neo Shadow
```css
.neo-shadow {
  @apply shadow-[4px_4px_0px_0px_rgba(0,0,0,1)];
}
```

#### Neo Card
```css
.neo-card {
  @apply neo-border neo-shadow bg-white transition-all duration-300;
}
```

#### Neo Button
```css
.neo-button {
  @apply neo-border neo-shadow px-6 py-3 font-bold transition-all duration-200 
         active:translate-x-[2px] active:translate-y-[2px] 
         active:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)];
}
```

### 2. Animation System

#### Keyframe Animations
- `float`: Gentle up-down movement (6s infinite)
- `slide-in`: Left-to-right entrance animation
- `fade-in`: Opacity transition
- `bounce-light`: Subtle bounce effect

#### Usage Examples
```tsx
// Staggered animations with delays
<div className="animate-slide-in [animation-delay:0.1s] opacity-0">
  <span className="neo-border bg-accent">Marketing Experts</span>
</div>
```

## Component Structure

### 1. Page Components (`src/pages/`)

#### Index.tsx - Landing Page
```tsx
const Index = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <Hero onBookingClick={handleBookingClick} />
        <Benefits />
        <Process />
        <Testimonials />
        <Pricing onBookingClick={handleBookingClick} />
      </main>
      <Footer />
    </div>
  );
};
```

#### Booking.tsx - Multi-Step Form
- Uses `FormProvider` context for state management
- Implements step-by-step navigation
- Features progress tracking and validation

### 2. Landing Page Sections (`src/components/`)

#### InteractiveLogo Component
- **Purpose**: Dynamic logo with online status and dropdown navigation
- **Features**: Time-based status indicator, dropdown menu, neo-brutalist styling
- **Props**: `className?: string`, `variant?: 'default' | 'compact'`, `showStatusText?: boolean`
- **Status Logic**: Green (8AM-5PM), Yellow (6-8AM, 5-10PM), Red (10PM-6AM)

#### Hero Component
- **Purpose**: Main landing section with CTA
- **Features**: Animated elements, floating shapes, responsive design
- **Props**: `onBookingClick: () => void`

#### Benefits Component
- **Purpose**: Showcase key value propositions
- **Features**: Icon-based cards with rotating layouts
- **Design**: 4-column grid with alternating colors

#### Process Component
- **Purpose**: Explain the 3-step process
- **Features**: Connected timeline, numbered steps
- **Design**: Horizontal flow with connecting line

#### Testimonials Component
- **Purpose**: Social proof through customer quotes
- **Features**: Rotated cards with quote marks
- **Design**: 3-column grid with decorative elements

#### Pricing Component
- **Purpose**: Single pricing tier presentation
- **Features**: Feature list, prominent CTA
- **Design**: Centered card with price tag overlay

### 3. Form Components (`src/components/booking/`)

#### Multi-Step Form Architecture

The booking form follows a wizard pattern with 4 steps:

1. **StepOne**: Basic information + website analysis
2. **StepTwo**: Business context and industry
3. **StepThree**: Marketing operations and budget
4. **StepFour**: Session goals and materials

#### Form Context Pattern
```tsx
interface FormContextType {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  currentStep: number;
  validateCurrentStep: () => boolean;
  errors: Record<string, string>;
  leadId: string | null;
}
```

#### Validation Strategy
- **Real-time validation**: Clear errors on user input
- **Step validation**: Prevent progression with invalid data
- **Type safety**: TypeScript interfaces for all form data

## UI Component Library

### Shadcn/ui Integration

The project uses shadcn/ui components as a foundation, customized with neo-brutalist styling:

#### Configuration (`components.json`)
```json
{
  "style": "default",
  "tailwind": {
    "baseColor": "slate",
    "cssVariables": true
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}
```

#### Key Components Used
- `Button`: Customized with neo-button classes
- `Input`: Enhanced with neo-input styling
- `RadioGroup`: Styled with neo-border containers
- `Progress`: Custom progress bar for form steps
- `Toast`: Notification system integration

### Custom Components

#### Multi-Select Component
- **Purpose**: Multiple option selection with search
- **Features**: Dropdown with checkboxes, search filtering
- **Usage**: Marketing areas, decision makers selection

#### Website Preview Component
- **Purpose**: Display website metadata and AI analysis
- **Features**: Automatic URL fetching, loading states
- **Integration**: Website analysis service

## State Management

### 1. Form State (React Context)

#### FormContext Provider
- Manages multi-step form data
- Handles validation and error states
- Provides navigation methods
- Integrates with database persistence

#### Key Features
- **Persistence**: Auto-save to localStorage and database
- **Recovery**: Resume incomplete forms
- **Validation**: Step-by-step validation rules
- **Type Safety**: Full TypeScript coverage

### 2. Database Integration

#### Neon Database Client
```typescript
import { sql } from '@/integrations/neon/client';

// Lead data persistence
const saveLeadData = async (formData: FormData) => {
  const result = await sql`
    INSERT INTO leads (email, first_name, last_name, ...)
    VALUES (${formData.email}, ${formData.firstName}, ...)
    RETURNING id
  `;
  return result[0].id;
};
```

## Responsive Design

### Breakpoint Strategy
- **Mobile First**: Base styles for mobile
- **Tablet**: `md:` prefix (768px+)
- **Desktop**: `lg:` prefix (1024px+)
- **Large Desktop**: `xl:` prefix (1280px+)

### Grid Layouts
```tsx
// Responsive grid example
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
  {items.map(item => (
    <div className="neo-card p-6">{item}</div>
  ))}
</div>
```

## Performance Optimizations

### 1. Code Splitting
- Route-based splitting with React Router
- Lazy loading for non-critical components

### 2. Asset Optimization
- Optimized images from Unsplash
- Font loading optimization
- CSS purging with Tailwind

### 3. Bundle Analysis
- Vite's built-in optimization
- Tree shaking for unused code
- Component tagger for development

## Development Workflow

### 1. Component Creation Process

1. **Design Review**: Confirm neo-brutalist styling requirements
2. **TypeScript Interface**: Define props and state types
3. **Component Structure**: Follow established patterns
4. **Styling**: Apply neo-classes and responsive design
5. **Testing**: Verify functionality and responsiveness
6. **Integration**: Connect to context/services as needed

### 2. Styling Guidelines

#### Class Naming Convention
```tsx
// Preferred pattern
<div className="neo-card p-6 bg-white hover:-translate-y-2">
  <h3 className="text-xl font-bold mb-3">{title}</h3>
  <p className="text-muted-foreground">{description}</p>
</div>
```

#### Color Application
```tsx
// Dynamic color assignment
style={{ 
  backgroundColor: index % 4 === 0 ? '#FFFFFF' : 
                   index % 4 === 1 ? '#FFD700' : 
                   index % 4 === 2 ? '#00A3FF' : 
                   '#FF3366',
  transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
}}
```

### 3. Form Component Patterns

#### Input Component Structure
```tsx
<div>
  <Label htmlFor="fieldName" className="block mb-1 font-medium">
    Field Label
  </Label>
  <Input 
    type="text"
    id="fieldName"
    name="fieldName"
    value={formData.fieldName}
    onChange={handleChange}
    className={`neo-input w-full ${errors.fieldName ? 'border-red-500' : ''}`}
  />
  {errors.fieldName && (
    <p className="mt-1 text-red-500 text-sm">{errors.fieldName}</p>
  )}
</div>
```

## Testing Strategy

### Component Testing
- Unit tests for individual components
- Integration tests for form workflows
- Visual regression testing for design consistency

### Form Validation Testing
- Test all validation rules
- Verify error message display
- Check step navigation logic

## Future Enhancements

### Planned Improvements
1. **Animation Library**: Framer Motion integration
2. **Theme System**: Dark mode support
3. **Component Storybook**: Documentation and testing
4. **Accessibility**: ARIA labels and keyboard navigation
5. **Performance**: Further optimization and monitoring

### Scalability Considerations
- Component library extraction
- Design token system
- Automated testing pipeline
- Performance monitoring integration
