
import React, { useState } from 'react';
import { X } from 'lucide-react';
import MultiSelect, { Option } from './ui/multi-select';

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MARKETING_AREAS: Option[] = [
  { value: 'paid-advertising', label: 'Paid advertising' },
  { value: 'growth-strategy', label: 'Growth strategy' },
  { value: 'content-marketing', label: 'Content marketing' },
  { value: 'seo', label: 'Search engine optimization' },
  { value: 'email-marketing', label: 'Email marketing' },
  { value: 'offline-marketing', label: 'Offline marketing' },
  { value: 'web-design', label: 'Web design' },
  { value: 'web-development', label: 'Web development' },
  { value: 'web-copy', label: 'Web copy' },
  { value: 'creative-production', label: 'Creative production' },
  { value: 'cro', label: 'CRO/conversion optimization' },
  { value: 'copywriting', label: 'Copywriting' },
  { value: 'social-media', label: 'Social media management' },
  { value: 'influencer-marketing', label: 'Influencer marketing' },
  { value: 'brand-strategy', label: 'Brand strategy' },
  { value: 'crm-analytics', label: 'CRM analytics' },
  { value: 'product-design', label: 'Product design' },
  { value: 'app-marketing', label: 'App marketing' },
  { value: 'marketing-automation', label: 'Marketing automation' },
  { value: 'personal-branding', label: 'Personal branding' },
];

const BookingModal: React.FC<BookingModalProps> = ({ isOpen, onClose }) => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    website: '',
    marketingAreas: [] as string[],
    additionalInfo: ''
  });
  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    website: '',
    marketingAreas: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleMarketingAreasChange = (selectedValues: string[]) => {
    setFormData(prev => ({ ...prev, marketingAreas: selectedValues }));
    
    // Clear error when user selects
    if (errors.marketingAreas) {
      setErrors(prev => ({ ...prev, marketingAreas: '' }));
    }
  };

  const validateStep1 = () => {
    let valid = true;
    const newErrors = { ...errors };
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
      valid = false;
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      valid = false;
    }
    
    if (!formData.website.trim()) {
      newErrors.website = 'Website is required';
      valid = false;
    } else {
      // Simple URL validation
      try {
        new URL(formData.website.startsWith('http') ? formData.website : `https://${formData.website}`);
      } catch (e) {
        newErrors.website = 'Please enter a valid URL';
        valid = false;
      }
    }
    
    setErrors(newErrors);
    return valid;
  };

  const validateStep2 = () => {
    let valid = true;
    const newErrors = { ...errors };
    
    if (formData.marketingAreas.length === 0) {
      newErrors.marketingAreas = 'Please select at least one area';
      valid = false;
    }
    
    setErrors(newErrors);
    return valid;
  };

  const handleNext = () => {
    if (step === 1 && validateStep1()) {
      setStep(2);
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (step === 2 && validateStep2()) {
      // In a real implementation, this would submit to a backend
      console.log('Form submitted:', formData);
      
      // For now, just show success and close
      alert('Booking successful! We will be in touch soon.');
      
      // Reset form and close
      setFormData({
        firstName: '',
        lastName: '',
        website: '',
        marketingAreas: [],
        additionalInfo: ''
      });
      setStep(1);
      onClose();
      
      // TODO: Future implementation - Integrate with Calendly for booking
      // TODO: Future implementation - Connect to Stripe for payment processing
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 animate-fade-in">
      <div 
        className="neo-card w-full max-w-md bg-white relative"
        style={{ transform: 'rotate(-1deg)' }}
      >
        {/* Close button */}
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-black hover:text-red-500"
        >
          <X size={24} />
        </button>
        
        {/* Progress indicator */}
        <div className="w-full h-4 bg-muted neo-border border-b-0 border-x-0">
          <div 
            className="h-full bg-primary transition-all duration-300"
            style={{ width: `${(step / 2) * 100}%` }}
          ></div>
        </div>
        
        <div className="p-6 md:p-8">
          {step === 1 && (
            <div className="animate-fade-in">
              <h2 className="text-2xl font-bold mb-2">30-Minute Marketing Consultation</h2>
              <p className="mb-6">Get strategic marketing guidance tailored to your needs!</p>
              
              <form>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="firstName" className="block mb-1 font-medium">
                      First Name
                    </label>
                    <input 
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className={`neo-input w-full ${errors.firstName ? 'border-red-500' : ''}`}
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-red-500 text-sm">{errors.firstName}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="lastName" className="block mb-1 font-medium">
                      Last Name
                    </label>
                    <input 
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className={`neo-input w-full ${errors.lastName ? 'border-red-500' : ''}`}
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-red-500 text-sm">{errors.lastName}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="website" className="block mb-1 font-medium">
                      Company Website
                    </label>
                    <input 
                      type="text"
                      id="website"
                      name="website"
                      value={formData.website}
                      onChange={handleChange}
                      placeholder="https://example.com"
                      className={`neo-input w-full ${errors.website ? 'border-red-500' : ''}`}
                    />
                    {errors.website && (
                      <p className="mt-1 text-red-500 text-sm">{errors.website}</p>
                    )}
                  </div>
                </div>
                
                <div className="mt-8">
                  <button 
                    type="button"
                    onClick={handleNext}
                    className="w-full neo-button bg-primary hover:bg-primary/90"
                  >
                    Next
                  </button>
                </div>
              </form>
            </div>
          )}
          
          {step === 2 && (
            <div className="animate-fade-in">
              <h2 className="text-2xl font-bold mb-2">Now let's learn more about your situation</h2>
              <p className="mb-6">Tell us what you need help with.</p>
              
              <form onSubmit={handleSubmit}>
                <div className="space-y-6">
                  <div>
                    <label className="block mb-2 font-medium">
                      What areas do you need help with?
                    </label>
                    <MultiSelect 
                      options={MARKETING_AREAS}
                      selectedValues={formData.marketingAreas}
                      onChange={handleMarketingAreasChange}
                      placeholder="Select marketing areas..."
                    />
                    {errors.marketingAreas && (
                      <p className="mt-1 text-red-500 text-sm">{errors.marketingAreas}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="additionalInfo" className="block mb-2 font-medium">
                      Anything else you'd like to share?
                    </label>
                    <textarea 
                      id="additionalInfo"
                      name="additionalInfo"
                      value={formData.additionalInfo}
                      onChange={handleChange}
                      rows={4}
                      className="neo-input w-full"
                    ></textarea>
                  </div>
                </div>
                
                <div className="mt-8 flex gap-4">
                  <button 
                    type="button"
                    onClick={handleBack}
                    className="flex-1 neo-button bg-white hover:bg-muted"
                  >
                    Back
                  </button>
                  <button 
                    type="submit"
                    className="flex-1 neo-button bg-primary hover:bg-primary/90"
                  >
                    Book Your Session
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BookingModal;
