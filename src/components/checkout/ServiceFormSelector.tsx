import React from 'react';
import { Service } from '../../data/services';
import LandingPageForm from './service-forms/LandingPageForm';
import WebsiteForm from './service-forms/WebsiteForm';
import BrandIdentityForm from './service-forms/BrandIdentityForm';
import MarketingAuditForm from './service-forms/MarketingAuditForm';

interface ServiceFormSelectorProps {
  service: Service;
  onNext: () => void;
}

const ServiceFormSelector: React.FC<ServiceFormSelectorProps> = ({ service, onNext }) => {
  // Select the appropriate form based on service ID
  switch (service.id) {
    case 'landing-page-design':
      return <LandingPageForm onNext={onNext} />;
    
    case 'website-development':
      return <WebsiteForm onNext={onNext} />;
    
    case 'brand-identity':
      return <BrandIdentityForm onNext={onNext} />;
    
    case 'marketing-audit':
      return <MarketingAuditForm onNext={onNext} />;
    
    default:
      // Fallback for any services without specific forms
      return <DefaultServiceForm service={service} onNext={onNext} />;
  }
};

// Default form for services that don't have specific forms yet
interface DefaultServiceFormProps {
  service: Service;
  onNext: () => void;
}

const DefaultServiceForm: React.FC<DefaultServiceFormProps> = ({ service, onNext }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-bold mb-4">{service.title} Details</h3>
        <p className="text-gray-600 mb-6">
          This service doesn't have a custom form yet. We'll collect your details and contact you to discuss your specific requirements.
        </p>
      </div>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="font-bold mb-2">What happens next?</h4>
        <ul className="text-sm space-y-1">
          <li>• Complete your payment</li>
          <li>• We'll contact you within 24 hours</li>
          <li>• Schedule a detailed consultation call</li>
          <li>• Gather all project requirements</li>
          <li>• Begin work on your {service.title.toLowerCase()}</li>
        </ul>
      </div>

      <button
        onClick={onNext}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
      >
        Continue to Payment
      </button>
    </div>
  );
};

export default ServiceFormSelector;
