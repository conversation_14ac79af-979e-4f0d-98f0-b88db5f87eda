import React from 'react';
import { User, Globe, ChevronDown } from 'lucide-react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useCheckout } from './CheckoutContext';

interface BrandInformationProps {
  onNext: () => void;
}

const BrandInformation: React.FC<BrandInformationProps> = ({ onNext }) => {
  const { checkoutData, updateCheckoutData, errors, setErrors } = useCheckout();

  const pageTypes = [
    'Product Landing Page',
    'Service Landing Page', 
    'Lead Generation Page',
    'Event Registration Page',
    'App Download Page',
    'E-commerce Product Page',
    'SaaS Signup Page',
    'Course Sales Page',
    'Webinar Landing Page',
    'Newsletter Signup Page'
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!checkoutData.brandName.trim()) {
      newErrors.brandName = 'Brand name is required';
    }

    if (!checkoutData.website.trim()) {
      newErrors.website = 'Website URL is required';
    } else if (!isValidUrl(checkoutData.website)) {
      newErrors.website = 'Please enter a valid URL';
    }

    if (!checkoutData.pageType) {
      newErrors.pageType = 'Please select a page type';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`);
      return true;
    } catch {
      return false;
    }
  };

  const handleInputChange = (field: keyof typeof checkoutData, value: string) => {
    updateCheckoutData({ [field]: value });
  };

  const handleContinue = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-8">
      <h2 className="text-2xl font-bold mb-6 text-blue-600">Brand Information</h2>
      
      <div className="space-y-6">
        {/* Brand Name */}
        <div>
          <Label htmlFor="brandName" className="block mb-2 font-medium flex items-center">
            <User className="h-4 w-4 mr-2" />
            Brand Name *
          </Label>
          <Input
            type="text"
            id="brandName"
            placeholder="Ex. Nike"
            value={checkoutData.brandName}
            onChange={(e) => handleInputChange('brandName', e.target.value)}
            className={`w-full ${errors.brandName ? 'border-red-500' : ''}`}
          />
          {errors.brandName && (
            <p className="mt-1 text-red-500 text-sm">{errors.brandName}</p>
          )}
        </div>

        {/* Website */}
        <div>
          <Label htmlFor="website" className="block mb-2 font-medium flex items-center">
            <Globe className="h-4 w-4 mr-2" />
            Website *
          </Label>
          <Input
            type="url"
            id="website"
            placeholder="www.website.com/product"
            value={checkoutData.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            className={`w-full ${errors.website ? 'border-red-500' : ''}`}
          />
          {errors.website && (
            <p className="mt-1 text-red-500 text-sm">{errors.website}</p>
          )}
        </div>

        {/* Page Type Selection */}
        <div>
          <Label className="block mb-2 font-medium">Select Landing Page Type</Label>
          <p className="text-sm text-gray-600 mb-4">
            Please select the type of landing page you are looking for. If you don't know, all good! 
            We can discuss and decide on the kick-off call.
          </p>
          
          <div className="mb-4">
            <button 
              type="button"
              className="text-blue-600 hover:underline text-sm"
              onClick={() => {
                // This could open a modal or expand help text
                alert('We\'ll help you choose the right page type based on your goals during our kick-off call.');
              }}
            >
              How do I choose the right pages?
            </button>
          </div>

          <Select 
            value={checkoutData.pageType} 
            onValueChange={(value) => handleInputChange('pageType', value)}
          >
            <SelectTrigger className={`w-full ${errors.pageType ? 'border-red-500' : ''}`}>
              <SelectValue placeholder="Select From List" />
            </SelectTrigger>
            <SelectContent>
              {pageTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.pageType && (
            <p className="mt-1 text-red-500 text-sm">{errors.pageType}</p>
          )}
        </div>

        {/* Kick-Off Call Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-bold mb-2">Kick-Off Call</h3>
          <p className="text-sm text-gray-700 mb-4">
            In order to get started we need a few additional pieces of information, which we 
            find is always best done on a call with you (and your team if you want!)
          </p>
          <p className="text-sm text-gray-700 mb-4">
            Once you complete your purchase you'll be given the option to book right away, or 
            at a later date.
          </p>
          <button 
            type="button"
            className="text-blue-600 hover:underline text-sm font-medium"
            onClick={() => {
              // This could open a modal with more details
              alert('The kick-off call typically takes 30-45 minutes and covers your goals, target audience, brand guidelines, and project timeline.');
            }}
          >
            What happens next?
          </button>
        </div>

        {/* Continue Button */}
        <button
          onClick={handleContinue}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
        >
          Continue to Payment
        </button>
      </div>
    </div>
  );
};

export default BrandInformation;
