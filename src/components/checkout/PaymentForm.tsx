import React, { useState } from 'react';
import { ArrowLeft, CreditCard, Mail, User, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useCheckout } from './CheckoutContext';
import { useToast } from '../../hooks/use-toast';
import { mockCreatePaymentIntent, mockConfirmPayment } from '../../api/stripe';
import { Service } from '../../data/services';
import { useAnalytics } from '../../services/analytics';

interface PaymentFormProps {
  onBack: () => void;
  service?: Service; // Make service optional for backward compatibility
}

const PaymentForm: React.FC<PaymentFormProps> = ({ onBack, service }) => {
  const { checkoutData, updateCheckoutData, isLoading, setIsLoading } = useCheckout();
  const { toast } = useToast();
  const navigate = useNavigate();
  const analytics = useAnalytics();
  const [paymentData, setPaymentData] = useState({
    nameOnCard: '',
    email: '',
    cardNumber: '',
    expiryDate: '',
    cvc: ''
  });

  const handleInputChange = (field: string, value: string) => {
    if (field === 'email') {
      updateCheckoutData({ email: value });
    }
    setPaymentData(prev => ({ ...prev, [field]: value }));
  };

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCardNumber(e.target.value);
    handleInputChange('cardNumber', formatted);
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatExpiryDate(e.target.value);
    handleInputChange('expiryDate', formatted);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Calculate amount based on service price
      const servicePrice = service?.price || 2500; // Default fallback
      const amount = typeof servicePrice === 'number' ? servicePrice * 100 : 250000; // Convert to cents

      // Step 1: Create payment intent
      const paymentIntentData = {
        amount,
        currency: service?.currency?.toLowerCase() || 'usd',
        customerEmail: paymentData.email,
        customerName: paymentData.nameOnCard,
        serviceId: service?.id || 'unknown',
        serviceName: service?.title || 'Service',
        // Include all relevant checkout data
        projectData: checkoutData,
      };

      const { paymentIntentId } = await mockCreatePaymentIntent(paymentIntentData);

      // Step 2: Confirm payment (in real implementation, this would use Stripe Elements)
      const result = await mockConfirmPayment(paymentIntentId);

      if (result.success) {
        // Track successful payment
        analytics.track('service_checkout_completed', {
          service_id: service?.id || 'unknown',
          service_name: service?.title || 'Service',
          service_price: typeof service?.price === 'number' ? service.price : undefined,
          payment_method: 'credit_card',
        });

        // Identify customer for future tracking
        analytics.identify(paymentData.email, {
          name: paymentData.nameOnCard,
          email: paymentData.email,
        });

        toast({
          title: "Payment Successful!",
          description: "Your order has been placed. You'll receive a confirmation email shortly.",
        });

        // Save order data (in real app, this would be handled by the backend)
        console.log('Order completed:', {
          paymentIntentId: result.paymentIntentId,
          service: {
            id: service?.id,
            title: service?.title,
            price: service?.price,
          },
          customer: {
            name: paymentData.nameOnCard,
            email: paymentData.email,
          },
          projectData: checkoutData,
          amount: servicePrice,
          status: 'paid'
        });

        // Redirect to success page
        navigate('/success');
      }

    } catch (error) {
      // Track payment failure
      analytics.track('service_checkout_abandoned', {
        service_id: service?.id || 'unknown',
        service_name: service?.title || 'Service',
        step: 'payment_processing',
      });

      toast({
        title: "Payment Failed",
        description: "There was an error processing your payment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-8">
      <div className="flex items-center mb-6">
        <button
          onClick={() => {
            analytics.track('service_checkout_abandoned', {
              service_id: service?.id || 'unknown',
              service_name: service?.title || 'Service',
              step: 'payment_form',
            });
            onBack();
          }}
          className="flex items-center text-blue-600 hover:text-blue-700 mr-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </button>
        <h2 className="text-2xl font-bold text-blue-600">Payment Information</h2>
      </div>

      {/* Service Summary */}
      {service && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-bold text-blue-800 mb-2">Order Summary</h3>
          <div className="flex justify-between items-center">
            <span className="text-blue-700">{service.title}</span>
            <span className="font-bold text-blue-800">
              {typeof service.price === 'number'
                ? `$${service.price.toLocaleString()} ${service.currency}`
                : 'Custom Quote'
              }
            </span>
          </div>
          <p className="text-sm text-blue-600 mt-1">
            Delivery: {service.deliveryTime}
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name on Card */}
        <div>
          <Label htmlFor="nameOnCard" className="block mb-2 font-medium">
            Name on Card
          </Label>
          <Input
            type="text"
            id="nameOnCard"
            value={paymentData.nameOnCard}
            onChange={(e) => handleInputChange('nameOnCard', e.target.value)}
            className="w-full"
            required
          />
        </div>

        {/* Email Address */}
        <div>
          <Label htmlFor="email" className="block mb-2 font-medium">
            Email Address
          </Label>
          <Input
            type="email"
            id="email"
            value={paymentData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full"
            required
          />
        </div>

        {/* Credit Card */}
        <div>
          <Label className="block mb-2 font-medium">Credit Card</Label>
          <div className="relative">
            <Input
              type="text"
              placeholder="Card number"
              value={paymentData.cardNumber}
              onChange={handleCardNumberChange}
              className="w-full pr-20"
              maxLength={19}
              required
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-1">
              <span className="text-xs text-gray-400">MM / YY</span>
              <span className="text-xs text-gray-400">CVC</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3 mt-3">
            <Input
              type="text"
              placeholder="MM/YY"
              value={paymentData.expiryDate}
              onChange={handleExpiryChange}
              maxLength={5}
              required
            />
            <Input
              type="text"
              placeholder="CVC"
              value={paymentData.cvc}
              onChange={(e) => handleInputChange('cvc', e.target.value.replace(/\D/g, '').substring(0, 4))}
              maxLength={4}
              required
            />
          </div>
        </div>

        {/* Pay Now Button */}
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-bold py-4 px-6 rounded-lg transition-colors"
        >
          {isLoading ? 'Processing...' : 'Pay Now'}
        </button>

        {/* Security Notice */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Your payment information is secure and encrypted
          </p>
        </div>
      </form>
    </div>
  );
};

export default PaymentForm;
