import React from 'react';
import { Check } from 'lucide-react';

const OrderSummary = () => {
  const includedFeatures = [
    'Copy, Strategy, Design',
    'Multiple Revisions',
    'Mobile & Desktop Design',
    'Figma File'
  ];

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-8">
      <h2 className="text-xl font-bold mb-6 text-blue-600">Order Summary</h2>
      
      {/* Service Item */}
      <div className="border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-lg">Landing Page Design</h3>
          <span className="text-xl font-bold text-blue-600">$2,500</span>
        </div>
        
        {/* Preview Image */}
        <div className="mb-4">
          <div className="w-20 h-16 bg-gray-100 rounded border flex items-center justify-center">
            <div className="text-xs text-gray-500 text-center">
              Design<br/>Preview
            </div>
          </div>
        </div>

        {/* Includes Section */}
        <div>
          <h4 className="font-medium mb-3">Includes:</h4>
          <ul className="space-y-2">
            {includedFeatures.map((feature, index) => (
              <li key={index} className="flex items-center text-sm">
                <div className="w-1.5 h-1.5 bg-black rounded-full mr-3"></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Estimated Turnaround */}
      <div className="flex justify-between items-center py-3 border-b border-gray-200">
        <span className="text-sm text-gray-600">Estimated Turnaround</span>
        <span className="font-medium">21 Days</span>
      </div>

      {/* Total */}
      <div className="flex justify-between items-center py-4 border-b border-gray-200">
        <span className="text-lg font-bold">Total</span>
        <span className="text-xl font-bold text-blue-600">$2,500</span>
      </div>

      {/* Currency Note */}
      <p className="text-xs text-gray-500 text-right mt-2">All prices in USD</p>

      {/* Trust Signals */}
      <div className="mt-6 space-y-3">
        <div className="flex items-center text-sm">
          <Check className="h-4 w-4 text-green-500 mr-2" />
          <span>30-day money-back guarantee</span>
        </div>
        <div className="flex items-center text-sm">
          <Check className="h-4 w-4 text-green-500 mr-2" />
          <span>Unlimited revisions included</span>
        </div>
        <div className="flex items-center text-sm">
          <Check className="h-4 w-4 text-green-500 mr-2" />
          <span>Professional design team</span>
        </div>
      </div>

      {/* Security Badge */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-center text-xs text-gray-500">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          Secure payment powered by Stripe
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
