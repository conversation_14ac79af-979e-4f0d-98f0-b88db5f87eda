import React from 'react';
import { User, Globe, BarChart3, Target } from 'lucide-react';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Textarea } from '../../ui/textarea';
import { useCheckout } from '../CheckoutContext';

interface MarketingAuditFormProps {
  onNext: () => void;
}

const MarketingAuditForm: React.FC<MarketingAuditFormProps> = ({ onNext }) => {
  const { checkoutData, updateCheckoutData, errors, setErrors } = useCheckout();

  const businessSizes = [
    'Solo entrepreneur',
    'Small business (2-10 employees)',
    'Medium business (11-50 employees)',
    'Large business (50+ employees)',
    'Enterprise (500+ employees)'
  ];

  const marketingChannels = [
    'Website/SEO',
    'Social Media',
    'Email Marketing',
    'Paid Advertising (Google, Facebook)',
    'Content Marketing',
    'Influencer Marketing',
    'Traditional Advertising',
    'Networking/Referrals',
    'Other'
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!checkoutData.brandName?.trim()) {
      newErrors.brandName = 'Brand name is required';
    }

    if (!checkoutData.website?.trim()) {
      newErrors.website = 'Website URL is required';
    }

    if (!checkoutData.businessSize) {
      newErrors.businessSize = 'Please select your business size';
    }

    if (!checkoutData.marketingGoals?.trim()) {
      newErrors.marketingGoals = 'Marketing goals are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    updateCheckoutData({ [field]: value });
  };

  const handleChannelChange = (channel: string, checked: boolean) => {
    const currentChannels = checkoutData.currentChannels ? checkoutData.currentChannels.split(',') : [];
    let updatedChannels;
    
    if (checked) {
      updatedChannels = [...currentChannels, channel];
    } else {
      updatedChannels = currentChannels.filter(c => c !== channel);
    }
    
    handleInputChange('currentChannels', updatedChannels.join(','));
  };

  const handleContinue = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-bold mb-4">Marketing Audit Details</h3>
        <p className="text-gray-600 mb-6">
          Help us understand your current marketing efforts so we can provide the most valuable audit and recommendations.
        </p>
      </div>

      {/* Brand Name */}
      <div>
        <Label htmlFor="brandName" className="block mb-2 font-medium flex items-center">
          <User className="h-4 w-4 mr-2" />
          Brand/Company Name *
        </Label>
        <Input
          type="text"
          id="brandName"
          placeholder="Your company name"
          value={checkoutData.brandName || ''}
          onChange={(e) => handleInputChange('brandName', e.target.value)}
          className={`w-full ${errors.brandName ? 'border-red-500' : ''}`}
        />
        {errors.brandName && (
          <p className="mt-1 text-red-500 text-sm">{errors.brandName}</p>
        )}
      </div>

      {/* Website */}
      <div>
        <Label htmlFor="website" className="block mb-2 font-medium flex items-center">
          <Globe className="h-4 w-4 mr-2" />
          Website URL *
        </Label>
        <Input
          type="url"
          id="website"
          placeholder="www.yourwebsite.com"
          value={checkoutData.website || ''}
          onChange={(e) => handleInputChange('website', e.target.value)}
          className={`w-full ${errors.website ? 'border-red-500' : ''}`}
        />
        {errors.website && (
          <p className="mt-1 text-red-500 text-sm">{errors.website}</p>
        )}
      </div>

      {/* Business Size */}
      <div>
        <Label className="block mb-2 font-medium flex items-center">
          <BarChart3 className="h-4 w-4 mr-2" />
          Business Size *
        </Label>
        <Select 
          value={checkoutData.businessSize || ''} 
          onValueChange={(value) => handleInputChange('businessSize', value)}
        >
          <SelectTrigger className={`w-full ${errors.businessSize ? 'border-red-500' : ''}`}>
            <SelectValue placeholder="Select your business size" />
          </SelectTrigger>
          <SelectContent>
            {businessSizes.map((size) => (
              <SelectItem key={size} value={size}>
                {size}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.businessSize && (
          <p className="mt-1 text-red-500 text-sm">{errors.businessSize}</p>
        )}
      </div>

      {/* Current Marketing Channels */}
      <div>
        <Label className="block mb-2 font-medium">
          Current Marketing Channels
        </Label>
        <p className="text-sm text-gray-600 mb-3">
          Select all marketing channels you're currently using:
        </p>
        <div className="grid grid-cols-2 gap-3">
          {marketingChannels.map((channel) => {
            const isSelected = checkoutData.currentChannels?.includes(channel) || false;
            return (
              <label key={channel} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={(e) => handleChannelChange(channel, e.target.checked)}
                  className="rounded border-gray-300"
                />
                <span className="text-sm">{channel}</span>
              </label>
            );
          })}
        </div>
      </div>

      {/* Marketing Goals */}
      <div>
        <Label htmlFor="marketingGoals" className="block mb-2 font-medium flex items-center">
          <Target className="h-4 w-4 mr-2" />
          Marketing Goals *
        </Label>
        <Textarea
          id="marketingGoals"
          placeholder="What are your main marketing goals? (e.g., increase leads, improve conversion rates, build brand awareness)"
          value={checkoutData.marketingGoals || ''}
          onChange={(e) => handleInputChange('marketingGoals', e.target.value)}
          className={`w-full h-24 ${errors.marketingGoals ? 'border-red-500' : ''}`}
        />
        {errors.marketingGoals && (
          <p className="mt-1 text-red-500 text-sm">{errors.marketingGoals}</p>
        )}
      </div>

      {/* Current Challenges */}
      <div>
        <Label htmlFor="challenges" className="block mb-2 font-medium">
          Current Marketing Challenges (Optional)
        </Label>
        <Textarea
          id="challenges"
          placeholder="What marketing challenges are you facing? What's not working well?"
          value={checkoutData.challenges || ''}
          onChange={(e) => handleInputChange('challenges', e.target.value)}
          className="w-full h-24"
        />
      </div>

      {/* Monthly Marketing Budget */}
      <div>
        <Label className="block mb-2 font-medium">
          Monthly Marketing Budget (Optional)
        </Label>
        <Select 
          value={checkoutData.monthlyBudget || ''} 
          onValueChange={(value) => handleInputChange('monthlyBudget', value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select your monthly marketing budget" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="under-1k">Under $1,000</SelectItem>
            <SelectItem value="1k-5k">$1,000 - $5,000</SelectItem>
            <SelectItem value="5k-10k">$5,000 - $10,000</SelectItem>
            <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
            <SelectItem value="25k+">$25,000+</SelectItem>
            <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Analytics Access */}
      <div>
        <Label htmlFor="analyticsAccess" className="block mb-2 font-medium">
          Analytics & Data Access
        </Label>
        <Textarea
          id="analyticsAccess"
          placeholder="Do you have Google Analytics, social media insights, or other data you can share for the audit?"
          value={checkoutData.analyticsAccess || ''}
          onChange={(e) => handleInputChange('analyticsAccess', e.target.value)}
          className="w-full h-20"
        />
      </div>

      {/* Continue Button */}
      <button
        onClick={handleContinue}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
      >
        Continue to Payment
      </button>
    </div>
  );
};

export default MarketingAuditForm;
