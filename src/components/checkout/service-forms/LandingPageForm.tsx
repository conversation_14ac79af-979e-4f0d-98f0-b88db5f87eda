import React from 'react';
import { User, Globe, Target } from 'lucide-react';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Textarea } from '../../ui/textarea';
import { useCheckout } from '../CheckoutContext';

interface LandingPageFormProps {
  onNext: () => void;
}

const LandingPageForm: React.FC<LandingPageFormProps> = ({ onNext }) => {
  const { checkoutData, updateCheckoutData, errors, setErrors } = useCheckout();

  const pageTypes = [
    'Product Landing Page',
    'Service Landing Page', 
    'Lead Generation Page',
    'Event Registration Page',
    'App Download Page',
    'E-commerce Product Page',
    'SaaS Signup Page',
    'Course Sales Page',
    'Webinar Landing Page',
    'Newsletter Signup Page'
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!checkoutData.brandName?.trim()) {
      newErrors.brandName = 'Brand name is required';
    }

    if (!checkoutData.website?.trim()) {
      newErrors.website = 'Website URL is required';
    } else if (!isValidUrl(checkoutData.website)) {
      newErrors.website = 'Please enter a valid URL';
    }

    if (!checkoutData.pageType) {
      newErrors.pageType = 'Please select a page type';
    }

    if (!checkoutData.targetAudience?.trim()) {
      newErrors.targetAudience = 'Target audience is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`);
      return true;
    } catch {
      return false;
    }
  };

  const handleInputChange = (field: string, value: string) => {
    updateCheckoutData({ [field]: value });
  };

  const handleContinue = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-bold mb-4">Landing Page Details</h3>
        <p className="text-gray-600 mb-6">
          Tell us about your landing page requirements so we can create the perfect design for your goals.
        </p>
      </div>

      {/* Brand Name */}
      <div>
        <Label htmlFor="brandName" className="block mb-2 font-medium flex items-center">
          <User className="h-4 w-4 mr-2" />
          Brand Name *
        </Label>
        <Input
          type="text"
          id="brandName"
          placeholder="Ex. Nike"
          value={checkoutData.brandName || ''}
          onChange={(e) => handleInputChange('brandName', e.target.value)}
          className={`w-full ${errors.brandName ? 'border-red-500' : ''}`}
        />
        {errors.brandName && (
          <p className="mt-1 text-red-500 text-sm">{errors.brandName}</p>
        )}
      </div>

      {/* Website */}
      <div>
        <Label htmlFor="website" className="block mb-2 font-medium flex items-center">
          <Globe className="h-4 w-4 mr-2" />
          Current Website *
        </Label>
        <Input
          type="url"
          id="website"
          placeholder="www.yourwebsite.com"
          value={checkoutData.website || ''}
          onChange={(e) => handleInputChange('website', e.target.value)}
          className={`w-full ${errors.website ? 'border-red-500' : ''}`}
        />
        {errors.website && (
          <p className="mt-1 text-red-500 text-sm">{errors.website}</p>
        )}
      </div>

      {/* Page Type Selection */}
      <div>
        <Label className="block mb-2 font-medium">Landing Page Type *</Label>
        <p className="text-sm text-gray-600 mb-4">
          Select the type of landing page you need. We'll discuss specifics during the kick-off call.
        </p>
        
        <Select 
          value={checkoutData.pageType || ''} 
          onValueChange={(value) => handleInputChange('pageType', value)}
        >
          <SelectTrigger className={`w-full ${errors.pageType ? 'border-red-500' : ''}`}>
            <SelectValue placeholder="Select page type" />
          </SelectTrigger>
          <SelectContent>
            {pageTypes.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.pageType && (
          <p className="mt-1 text-red-500 text-sm">{errors.pageType}</p>
        )}
      </div>

      {/* Target Audience */}
      <div>
        <Label htmlFor="targetAudience" className="block mb-2 font-medium flex items-center">
          <Target className="h-4 w-4 mr-2" />
          Target Audience *
        </Label>
        <Textarea
          id="targetAudience"
          placeholder="Describe your target audience (e.g., small business owners, tech professionals, etc.)"
          value={checkoutData.targetAudience || ''}
          onChange={(e) => handleInputChange('targetAudience', e.target.value)}
          className={`w-full h-24 ${errors.targetAudience ? 'border-red-500' : ''}`}
        />
        {errors.targetAudience && (
          <p className="mt-1 text-red-500 text-sm">{errors.targetAudience}</p>
        )}
      </div>

      {/* Primary Goal */}
      <div>
        <Label htmlFor="primaryGoal" className="block mb-2 font-medium">
          Primary Goal (Optional)
        </Label>
        <Textarea
          id="primaryGoal"
          placeholder="What's the main goal of this landing page? (e.g., generate leads, sell product, increase signups)"
          value={checkoutData.primaryGoal || ''}
          onChange={(e) => handleInputChange('primaryGoal', e.target.value)}
          className="w-full h-20"
        />
      </div>

      {/* Continue Button */}
      <button
        onClick={handleContinue}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
      >
        Continue to Payment
      </button>
    </div>
  );
};

export default LandingPageForm;
