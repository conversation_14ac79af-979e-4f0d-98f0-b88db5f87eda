import React from 'react';
import { User, Building, Palette, Target } from 'lucide-react';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Textarea } from '../../ui/textarea';
import { useCheckout } from '../CheckoutContext';

interface BrandIdentityFormProps {
  onNext: () => void;
}

const BrandIdentityForm: React.FC<BrandIdentityFormProps> = ({ onNext }) => {
  const { checkoutData, updateCheckoutData, errors, setErrors } = useCheckout();

  const industries = [
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'Retail/E-commerce',
    'Food & Beverage',
    'Real Estate',
    'Consulting',
    'Creative Services',
    'Non-profit',
    'Other'
  ];

  const brandStyles = [
    'Modern & Minimalist',
    'Bold & Energetic',
    'Professional & Corporate',
    'Creative & Artistic',
    'Friendly & Approachable',
    'Luxury & Premium',
    'Tech & Innovative',
    'Traditional & Classic'
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!checkoutData.brandName?.trim()) {
      newErrors.brandName = 'Brand name is required';
    }

    if (!checkoutData.industry) {
      newErrors.industry = 'Please select your industry';
    }

    if (!checkoutData.brandDescription?.trim()) {
      newErrors.brandDescription = 'Brand description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    updateCheckoutData({ [field]: value });
  };

  const handleContinue = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-bold mb-4">Brand Identity Details</h3>
        <p className="text-gray-600 mb-6">
          Help us understand your brand so we can create an identity that perfectly represents your business.
        </p>
      </div>

      {/* Brand Name */}
      <div>
        <Label htmlFor="brandName" className="block mb-2 font-medium flex items-center">
          <User className="h-4 w-4 mr-2" />
          Brand/Company Name *
        </Label>
        <Input
          type="text"
          id="brandName"
          placeholder="Your brand name"
          value={checkoutData.brandName || ''}
          onChange={(e) => handleInputChange('brandName', e.target.value)}
          className={`w-full ${errors.brandName ? 'border-red-500' : ''}`}
        />
        {errors.brandName && (
          <p className="mt-1 text-red-500 text-sm">{errors.brandName}</p>
        )}
      </div>

      {/* Industry */}
      <div>
        <Label className="block mb-2 font-medium flex items-center">
          <Building className="h-4 w-4 mr-2" />
          Industry *
        </Label>
        <Select 
          value={checkoutData.industry || ''} 
          onValueChange={(value) => handleInputChange('industry', value)}
        >
          <SelectTrigger className={`w-full ${errors.industry ? 'border-red-500' : ''}`}>
            <SelectValue placeholder="Select your industry" />
          </SelectTrigger>
          <SelectContent>
            {industries.map((industry) => (
              <SelectItem key={industry} value={industry}>
                {industry}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.industry && (
          <p className="mt-1 text-red-500 text-sm">{errors.industry}</p>
        )}
      </div>

      {/* Brand Description */}
      <div>
        <Label htmlFor="brandDescription" className="block mb-2 font-medium">
          Brand Description *
        </Label>
        <Textarea
          id="brandDescription"
          placeholder="Describe your brand, what you do, your values, and what makes you unique"
          value={checkoutData.brandDescription || ''}
          onChange={(e) => handleInputChange('brandDescription', e.target.value)}
          className={`w-full h-32 ${errors.brandDescription ? 'border-red-500' : ''}`}
        />
        {errors.brandDescription && (
          <p className="mt-1 text-red-500 text-sm">{errors.brandDescription}</p>
        )}
      </div>

      {/* Target Audience */}
      <div>
        <Label htmlFor="targetAudience" className="block mb-2 font-medium flex items-center">
          <Target className="h-4 w-4 mr-2" />
          Target Audience
        </Label>
        <Textarea
          id="targetAudience"
          placeholder="Who is your ideal customer? (demographics, interests, needs)"
          value={checkoutData.targetAudience || ''}
          onChange={(e) => handleInputChange('targetAudience', e.target.value)}
          className="w-full h-24"
        />
      </div>

      {/* Brand Style Preference */}
      <div>
        <Label className="block mb-2 font-medium flex items-center">
          <Palette className="h-4 w-4 mr-2" />
          Preferred Brand Style
        </Label>
        <Select 
          value={checkoutData.brandStyle || ''} 
          onValueChange={(value) => handleInputChange('brandStyle', value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a style that appeals to you" />
          </SelectTrigger>
          <SelectContent>
            {brandStyles.map((style) => (
              <SelectItem key={style} value={style}>
                {style}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Color Preferences */}
      <div>
        <Label htmlFor="colorPreferences" className="block mb-2 font-medium">
          Color Preferences (Optional)
        </Label>
        <Input
          type="text"
          id="colorPreferences"
          placeholder="Any specific colors you love or want to avoid?"
          value={checkoutData.colorPreferences || ''}
          onChange={(e) => handleInputChange('colorPreferences', e.target.value)}
          className="w-full"
        />
      </div>

      {/* Competitors/Inspiration */}
      <div>
        <Label htmlFor="inspiration" className="block mb-2 font-medium">
          Inspiration/Competitors (Optional)
        </Label>
        <Textarea
          id="inspiration"
          placeholder="Any brands you admire or competitors we should be aware of?"
          value={checkoutData.inspiration || ''}
          onChange={(e) => handleInputChange('inspiration', e.target.value)}
          className="w-full h-20"
        />
      </div>

      {/* Applications Needed */}
      <div>
        <Label htmlFor="applications" className="block mb-2 font-medium">
          Where will you use this brand? (Optional)
        </Label>
        <Textarea
          id="applications"
          placeholder="Website, business cards, signage, social media, packaging, etc."
          value={checkoutData.applications || ''}
          onChange={(e) => handleInputChange('applications', e.target.value)}
          className="w-full h-20"
        />
      </div>

      {/* Continue Button */}
      <button
        onClick={handleContinue}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
      >
        Continue to Payment
      </button>
    </div>
  );
};

export default BrandIdentityForm;
