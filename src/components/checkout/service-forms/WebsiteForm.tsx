import React from 'react';
import { User, Globe, Code, Layers } from 'lucide-react';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Textarea } from '../../ui/textarea';
import { useCheckout } from '../CheckoutContext';

interface WebsiteFormProps {
  onNext: () => void;
}

const WebsiteForm: React.FC<WebsiteFormProps> = ({ onNext }) => {
  const { checkoutData, updateCheckoutData, errors, setErrors } = useCheckout();

  const websiteTypes = [
    'Business Website',
    'E-commerce Store',
    'Portfolio Website',
    'Blog/Content Site',
    'SaaS Application',
    'Landing Page Collection',
    'Membership Site',
    'Educational Platform'
  ];

  const techPreferences = [
    'No preference (recommend best option)',
    'React/Next.js (recommended)',
    'WordPress',
    'Shopify (for e-commerce)',
    'Custom solution'
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!checkoutData.brandName?.trim()) {
      newErrors.brandName = 'Brand name is required';
    }

    if (!checkoutData.websiteType) {
      newErrors.websiteType = 'Please select a website type';
    }

    if (!checkoutData.projectDescription?.trim()) {
      newErrors.projectDescription = 'Project description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    updateCheckoutData({ [field]: value });
  };

  const handleContinue = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-bold mb-4">Website Development Details</h3>
        <p className="text-gray-600 mb-6">
          Tell us about your website requirements so we can build exactly what you need.
        </p>
      </div>

      {/* Brand Name */}
      <div>
        <Label htmlFor="brandName" className="block mb-2 font-medium flex items-center">
          <User className="h-4 w-4 mr-2" />
          Brand/Company Name *
        </Label>
        <Input
          type="text"
          id="brandName"
          placeholder="Your company name"
          value={checkoutData.brandName || ''}
          onChange={(e) => handleInputChange('brandName', e.target.value)}
          className={`w-full ${errors.brandName ? 'border-red-500' : ''}`}
        />
        {errors.brandName && (
          <p className="mt-1 text-red-500 text-sm">{errors.brandName}</p>
        )}
      </div>

      {/* Current Website */}
      <div>
        <Label htmlFor="currentWebsite" className="block mb-2 font-medium flex items-center">
          <Globe className="h-4 w-4 mr-2" />
          Current Website (Optional)
        </Label>
        <Input
          type="url"
          id="currentWebsite"
          placeholder="www.currentsite.com (if you have one)"
          value={checkoutData.currentWebsite || ''}
          onChange={(e) => handleInputChange('currentWebsite', e.target.value)}
          className="w-full"
        />
      </div>

      {/* Website Type */}
      <div>
        <Label className="block mb-2 font-medium flex items-center">
          <Layers className="h-4 w-4 mr-2" />
          Website Type *
        </Label>
        <Select 
          value={checkoutData.websiteType || ''} 
          onValueChange={(value) => handleInputChange('websiteType', value)}
        >
          <SelectTrigger className={`w-full ${errors.websiteType ? 'border-red-500' : ''}`}>
            <SelectValue placeholder="Select website type" />
          </SelectTrigger>
          <SelectContent>
            {websiteTypes.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.websiteType && (
          <p className="mt-1 text-red-500 text-sm">{errors.websiteType}</p>
        )}
      </div>

      {/* Technology Preference */}
      <div>
        <Label className="block mb-2 font-medium flex items-center">
          <Code className="h-4 w-4 mr-2" />
          Technology Preference
        </Label>
        <Select 
          value={checkoutData.techPreference || ''} 
          onValueChange={(value) => handleInputChange('techPreference', value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select technology preference" />
          </SelectTrigger>
          <SelectContent>
            {techPreferences.map((tech) => (
              <SelectItem key={tech} value={tech}>
                {tech}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Project Description */}
      <div>
        <Label htmlFor="projectDescription" className="block mb-2 font-medium">
          Project Description *
        </Label>
        <Textarea
          id="projectDescription"
          placeholder="Describe your website requirements, features needed, design preferences, etc."
          value={checkoutData.projectDescription || ''}
          onChange={(e) => handleInputChange('projectDescription', e.target.value)}
          className={`w-full h-32 ${errors.projectDescription ? 'border-red-500' : ''}`}
        />
        {errors.projectDescription && (
          <p className="mt-1 text-red-500 text-sm">{errors.projectDescription}</p>
        )}
      </div>

      {/* Key Features */}
      <div>
        <Label htmlFor="keyFeatures" className="block mb-2 font-medium">
          Key Features Needed (Optional)
        </Label>
        <Textarea
          id="keyFeatures"
          placeholder="List specific features you need (e.g., contact forms, online booking, payment processing, user accounts, etc.)"
          value={checkoutData.keyFeatures || ''}
          onChange={(e) => handleInputChange('keyFeatures', e.target.value)}
          className="w-full h-24"
        />
      </div>

      {/* Timeline */}
      <div>
        <Label htmlFor="timeline" className="block mb-2 font-medium">
          Preferred Timeline
        </Label>
        <Select 
          value={checkoutData.timeline || ''} 
          onValueChange={(value) => handleInputChange('timeline', value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="When do you need this completed?" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="asap">ASAP</SelectItem>
            <SelectItem value="1-month">Within 1 month</SelectItem>
            <SelectItem value="2-months">Within 2 months</SelectItem>
            <SelectItem value="3-months">Within 3 months</SelectItem>
            <SelectItem value="flexible">Flexible timeline</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Continue Button */}
      <button
        onClick={handleContinue}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
      >
        Continue to Payment
      </button>
    </div>
  );
};

export default WebsiteForm;
