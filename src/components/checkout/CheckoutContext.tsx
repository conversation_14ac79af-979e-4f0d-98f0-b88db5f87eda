import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface CheckoutData {
  // Common fields
  brandName: string;
  website: string;
  email: string;
  name: string;

  // Landing Page specific
  pageType?: string;
  targetAudience?: string;
  primaryGoal?: string;

  // Website Development specific
  currentWebsite?: string;
  websiteType?: string;
  techPreference?: string;
  projectDescription?: string;
  keyFeatures?: string;
  timeline?: string;

  // Brand Identity specific
  industry?: string;
  brandDescription?: string;
  brandStyle?: string;
  colorPreferences?: string;
  inspiration?: string;
  applications?: string;

  // Marketing Audit specific
  businessSize?: string;
  currentChannels?: string;
  marketingGoals?: string;
  challenges?: string;
  monthlyBudget?: string;
  analyticsAccess?: string;
}

interface CheckoutContextType {
  checkoutData: CheckoutData;
  updateCheckoutData: (data: Partial<CheckoutData>) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  errors: Record<string, string>;
  setErrors: (errors: Record<string, string>) => void;
}

const CheckoutContext = createContext<CheckoutContextType | undefined>(undefined);

export const useCheckout = () => {
  const context = useContext(CheckoutContext);
  if (!context) {
    throw new Error('useCheckout must be used within a CheckoutProvider');
  }
  return context;
};

interface CheckoutProviderProps {
  children: ReactNode;
}

export const CheckoutProvider: React.FC<CheckoutProviderProps> = ({ children }) => {
  const [checkoutData, setCheckoutData] = useState<CheckoutData>({
    brandName: '',
    website: '',
    email: '',
    name: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateCheckoutData = (data: Partial<CheckoutData>) => {
    setCheckoutData(prev => ({ ...prev, ...data }));
    // Clear errors for updated fields
    const updatedErrors = { ...errors };
    Object.keys(data).forEach(key => {
      delete updatedErrors[key];
    });
    setErrors(updatedErrors);
  };

  const value: CheckoutContextType = {
    checkoutData,
    updateCheckoutData,
    isLoading,
    setIsLoading,
    errors,
    setErrors,
  };

  return (
    <CheckoutContext.Provider value={value}>
      {children}
    </CheckoutContext.Provider>
  );
};
