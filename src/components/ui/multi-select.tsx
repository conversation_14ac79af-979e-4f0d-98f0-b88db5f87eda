
import React, { useState, useRef, useEffect } from 'react';
import { Check, X, Search } from 'lucide-react';

export interface Option {
  value: string;
  label: string;
}

interface MultiSelectProps {
  options: Option[];
  selectedValues: string[];
  onChange: (selectedValues: string[]) => void;
  placeholder?: string;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  selectedValues,
  onChange,
  placeholder = 'Select options...'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Filter options based on search term
  const filteredOptions = options.filter(option => 
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle option selection
  const toggleOption = (value: string) => {
    if (selectedValues.includes(value)) {
      onChange(selectedValues.filter(val => val !== value));
    } else {
      onChange([...selectedValues, value]);
    }
  };

  // Remove a selected option
  const removeOption = (value: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(selectedValues.filter(val => val !== value));
  };

  return (
    <div className="relative w-full" ref={containerRef}>
      {/* Selected options and search input */}
      <div 
        className="neo-input w-full min-h-[50px] flex flex-wrap gap-2 p-2 cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        {selectedValues.length > 0 ? (
          <>
            {selectedValues.map(value => {
              const option = options.find(opt => opt.value === value);
              return (
                <div 
                  key={value}
                  className="flex items-center gap-1 py-1 px-2 bg-primary/20 neo-border"
                >
                  <span>{option?.label}</span>
                  <button 
                    onClick={(e) => removeOption(value, e)}
                    className="ml-1 text-black hover:text-red-500"
                  >
                    <X size={14} />
                  </button>
                </div>
              );
            })}
          </>
        ) : (
          <div className="p-1 text-muted-foreground">{placeholder}</div>
        )}
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 neo-border bg-white max-h-60 overflow-auto animate-fade-in">
          {/* Search input */}
          <div className="sticky top-0 p-2 bg-white border-b-2 border-black">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
              <input
                type="text"
                className="w-full pl-8 pr-4 py-2 neo-border focus:outline-none"
                placeholder="Search options..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>

          {/* Options list */}
          <div className="p-2">
            {filteredOptions.length > 0 ? (
              filteredOptions.map(option => (
                <div
                  key={option.value}
                  className={`p-2 cursor-pointer flex items-center ${
                    selectedValues.includes(option.value) 
                      ? 'bg-primary/20' 
                      : 'hover:bg-muted'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleOption(option.value);
                  }}
                >
                  <div className={`w-5 h-5 mr-2 neo-border flex items-center justify-center ${
                    selectedValues.includes(option.value) ? 'bg-primary' : 'bg-white'
                  }`}>
                    {selectedValues.includes(option.value) && (
                      <Check size={14} className="text-black" />
                    )}
                  </div>
                  <span>{option.label}</span>
                </div>
              ))
            ) : (
              <div className="p-2 text-center text-muted-foreground">
                No options found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelect;
