import React, { useState, useEffect } from 'react';
import { dynamicFaviconService, OnlineStatus } from '../services/dynamicFaviconService';

/**
 * Debug component for testing the dynamic favicon functionality
 * This component is only for development/testing purposes
 */
const FaviconDebugger: React.FC = () => {
  const [currentStatus, setCurrentStatus] = useState<{ status: OnlineStatus; color: string }>({
    status: 'offline',
    color: '#ef4444'
  });
  const [animationState, setAnimationState] = useState<{ isAnimating: boolean; showingProfile: boolean }>({
    isAnimating: false,
    showingProfile: false
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Update status and animation state every second for real-time display
    const updateState = () => {
      setCurrentStatus(dynamicFaviconService.getCurrentStatus());
      setAnimationState(dynamicFaviconService.getAnimationState());
    };

    updateState();
    const interval = setInterval(updateState, 1000);

    return () => clearInterval(interval);
  }, []);

  // Only show in development mode
  if (import.meta.env.PROD) {
    return null;
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-black text-white px-3 py-2 rounded text-xs font-mono"
          title="Show Favicon Debugger"
        >
          🎯
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white border-2 border-black rounded-lg p-4 shadow-lg max-w-xs">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-bold text-sm">Favicon Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-black"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-2 text-xs">
        <div className="flex items-center gap-2">
          <div 
            className="w-3 h-3 rounded-full border border-black"
            style={{ backgroundColor: currentStatus.color }}
          />
          <span className="font-mono">Status: {currentStatus.status}</span>
        </div>
        
        <div className="font-mono">
          Color: {currentStatus.color}
        </div>
        
        <div className="font-mono">
          Time: {new Date().toLocaleTimeString()}
        </div>

        <div className="font-mono">
          Animation: {animationState.isAnimating ? '🔄 ON' : '⏸️ OFF'}
        </div>

        <div className="font-mono">
          Showing: {animationState.showingProfile ? '👤 Profile' : '🟢 Status'}
        </div>

        <div className="pt-2 border-t border-gray-200 space-y-1">
          <button
            onClick={() => dynamicFaviconService.forceUpdate()}
            className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600 w-full"
          >
            Force Update
          </button>

          <button
            onClick={() => dynamicFaviconService.toggleAnimation()}
            className={`px-2 py-1 rounded text-xs w-full ${
              animationState.isAnimating
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            {animationState.isAnimating ? 'Stop Animation' : 'Start Animation'}
          </button>
        </div>
        
        <div className="text-gray-600 text-xs">
          <div>Online: 8AM-5PM</div>
          <div>Away: 6-8AM, 5-10PM</div>
          <div>Offline: 10PM-6AM</div>
        </div>
      </div>
    </div>
  );
};

export default FaviconDebugger;
