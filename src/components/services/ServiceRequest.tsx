import React, { useState } from 'react';
import { ArrowLeft, Calendar, Users, MapPin, Clock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useToast } from '../../hooks/use-toast';
import { Service } from '../../data/services';

interface ServiceRequestProps {
  service: Service;
  onBack: () => void;
}

interface RequestFormData {
  name: string;
  email: string;
  company: string;
  phone: string;
  projectDescription: string;
  timeline: string;
  budget: string;
  additionalInfo: string;
  // Specific fields for different service types
  eventDate?: string;
  eventLocation?: string;
  audienceSize?: string;
  workshopDuration?: string;
}

const ServiceRequest: React.FC<ServiceRequestProps> = ({ service, onBack }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<RequestFormData>({
    name: '',
    email: '',
    company: '',
    phone: '',
    projectDescription: '',
    timeline: '',
    budget: '',
    additionalInfo: '',
    eventDate: '',
    eventLocation: '',
    audienceSize: '',
    workshopDuration: '',
  });

  const handleInputChange = (field: keyof RequestFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.name.trim() || !formData.email.trim() || !formData.projectDescription.trim()) {
        throw new Error('Please fill in all required fields');
      }

      // Simulate API call to submit consultation request
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Prepare submission data
      const submissionData = {
        service: {
          id: service.id,
          title: service.title,
          type: service.type,
        },
        customer: {
          name: formData.name,
          email: formData.email,
          company: formData.company,
          phone: formData.phone,
        },
        project: {
          description: formData.projectDescription,
          timeline: formData.timeline,
          budget: formData.budget,
          additionalInfo: formData.additionalInfo,
        },
        serviceSpecific: {
          // Include service-specific fields
          ...(service.id === 'public-speaking' && {
            eventDate: formData.eventDate,
            eventLocation: formData.eventLocation,
            audienceSize: formData.audienceSize,
          }),
          ...(service.id === 'marketing-workshop' && {
            workshopDuration: formData.workshopDuration,
            audienceSize: formData.audienceSize,
          }),
        },
        submittedAt: new Date().toISOString(),
        status: 'pending_review'
      };

      console.log('Consultation request submitted:', submissionData);

      toast({
        title: "Request Submitted Successfully!",
        description: "We'll review your request and get back to you within 24 hours with a custom proposal.",
      });

      // Redirect to request success page
      navigate('/request-success');

    } catch (error) {
      toast({
        title: "Submission Failed",
        description: error instanceof Error ? error.message : "There was an error submitting your request. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderServiceSpecificFields = () => {
    if (service.id === 'public-speaking') {
      return (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="eventDate" className="block mb-2 font-medium flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Event Date
              </Label>
              <Input
                type="date"
                id="eventDate"
                value={formData.eventDate}
                onChange={(e) => handleInputChange('eventDate', e.target.value)}
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="eventLocation" className="block mb-2 font-medium flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                Event Location
              </Label>
              <Input
                type="text"
                id="eventLocation"
                placeholder="City, Country or Virtual"
                value={formData.eventLocation}
                onChange={(e) => handleInputChange('eventLocation', e.target.value)}
                className="w-full"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="audienceSize" className="block mb-2 font-medium flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Expected Audience Size
            </Label>
            <Select value={formData.audienceSize} onValueChange={(value) => handleInputChange('audienceSize', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select audience size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1-50">1-50 people</SelectItem>
                <SelectItem value="51-100">51-100 people</SelectItem>
                <SelectItem value="101-500">101-500 people</SelectItem>
                <SelectItem value="500+">500+ people</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      );
    }

    if (service.id === 'marketing-workshop') {
      return (
        <>
          <div>
            <Label htmlFor="workshopDuration" className="block mb-2 font-medium flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Preferred Workshop Duration
            </Label>
            <Select value={formData.workshopDuration} onValueChange={(value) => handleInputChange('workshopDuration', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="half-day">Half Day (4 hours)</SelectItem>
                <SelectItem value="full-day">Full Day (8 hours)</SelectItem>
                <SelectItem value="two-day">Two Days (16 hours)</SelectItem>
                <SelectItem value="custom">Custom Duration</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="audienceSize" className="block mb-2 font-medium flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Number of Participants
            </Label>
            <Select value={formData.audienceSize} onValueChange={(value) => handleInputChange('audienceSize', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select number of participants" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1-10">1-10 people</SelectItem>
                <SelectItem value="11-25">11-25 people</SelectItem>
                <SelectItem value="26-50">26-50 people</SelectItem>
                <SelectItem value="50+">50+ people</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      );
    }

    return null;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-8">
      {/* Header */}
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="flex items-center text-blue-600 hover:text-blue-700 mr-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Service
        </button>
        <h2 className="text-2xl font-bold text-blue-600">
          Request Quote - {service.title}
        </h2>
      </div>

      <div className="mb-6">
        <p className="text-gray-600">
          Please provide details about your project so we can prepare a customized proposal for you.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name" className="block mb-2 font-medium">
              Full Name *
            </Label>
            <Input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full"
              required
            />
          </div>
          <div>
            <Label htmlFor="email" className="block mb-2 font-medium">
              Email Address *
            </Label>
            <Input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="company" className="block mb-2 font-medium">
              Company/Organization
            </Label>
            <Input
              type="text"
              id="company"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="phone" className="block mb-2 font-medium">
              Phone Number
            </Label>
            <Input
              type="tel"
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        {/* Service-specific fields */}
        {renderServiceSpecificFields()}

        {/* Project Details */}
        <div>
          <Label htmlFor="projectDescription" className="block mb-2 font-medium">
            Project Description *
          </Label>
          <Textarea
            id="projectDescription"
            placeholder={`Tell us about your ${service.title.toLowerCase()} requirements...`}
            value={formData.projectDescription}
            onChange={(e) => handleInputChange('projectDescription', e.target.value)}
            className="w-full h-32"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="timeline" className="block mb-2 font-medium">
              Preferred Timeline
            </Label>
            <Select value={formData.timeline} onValueChange={(value) => handleInputChange('timeline', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select timeline" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asap">ASAP</SelectItem>
                <SelectItem value="1-month">Within 1 month</SelectItem>
                <SelectItem value="2-3-months">2-3 months</SelectItem>
                <SelectItem value="flexible">Flexible</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="budget" className="block mb-2 font-medium">
              Budget Range
            </Label>
            <Select value={formData.budget} onValueChange={(value) => handleInputChange('budget', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select budget range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="under-5k">Under $5,000</SelectItem>
                <SelectItem value="5k-10k">$5,000 - $10,000</SelectItem>
                <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
                <SelectItem value="25k+">$25,000+</SelectItem>
                <SelectItem value="discuss">Let's discuss</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="additionalInfo" className="block mb-2 font-medium">
            Additional Information
          </Label>
          <Textarea
            id="additionalInfo"
            placeholder="Any additional details, special requirements, or questions..."
            value={formData.additionalInfo}
            onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
            className="w-full h-24"
          />
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-bold py-4 px-6 rounded-lg transition-colors"
        >
          {isSubmitting ? 'Submitting Request...' : 'Submit Request'}
        </button>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            We'll review your request and get back to you within 24 hours with a customized proposal.
          </p>
        </div>
      </form>
    </div>
  );
};

export default ServiceRequest;
