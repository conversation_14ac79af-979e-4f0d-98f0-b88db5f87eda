import React, { useState } from 'react';
import { <PERSON>R<PERSON>, Star, Filter, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import { services, Service } from '../../data/services';
import { useScrollReveal } from '../../hooks/useScrollReveal';

// Utility function for formatting prices
const formatPrice = (price: number | 'custom', currency: string) => {
  if (price === 'custom') return 'Custom Quote';
  return `$${price.toLocaleString()} ${currency}`;
};

const ServicesOverview = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  // Initialize scroll reveal animations
  useScrollReveal();

  const categories = [
    { id: 'all', label: 'All Services', count: services.length },
    { id: 'design', label: 'Design', count: services.filter(s => s.category === 'design').length },
    { id: 'development', label: 'Development', count: services.filter(s => s.category === 'development').length },
    { id: 'marketing', label: 'Marketing', count: services.filter(s => s.category === 'marketing').length },
    { id: 'consulting', label: 'Consulting', count: services.filter(s => s.category === 'consulting').length },
  ];

  const filteredServices = selectedCategory === 'all'
    ? services
    : services.filter(service => service.category === selectedCategory);

  return (
    <div className="pt-32 pb-20 overflow-hidden relative">
      {/* Floating Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary neo-border rotate-12 animate-float opacity-20"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-accent neo-border -rotate-6 animate-float opacity-20" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-secondary neo-border rotate-3 animate-float opacity-20" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-primary neo-border -rotate-12 animate-float opacity-20" style={{ animationDelay: '3s' }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="animate-slide-in [animation-delay:0.1s] opacity-0">
            <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
              OUR SERVICES
            </span>
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6 animate-slide-in [animation-delay:0.2s] opacity-0">
            Everything You Need to <span className="relative inline-block">
              Grow Your Business
              <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
                <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
              </svg>
            </span>
          </h1>
          
          <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto animate-slide-in [animation-delay:0.3s] opacity-0">
            From design to development, marketing to consulting - we offer comprehensive solutions 
            to help your business thrive in the digital world.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12 animate-slide-in [animation-delay:0.4s] opacity-0">
          {categories.map((category, index) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`neo-button px-6 py-3 font-bold transition-all duration-300 hover-tilt ${
                selectedCategory === category.id
                  ? 'bg-primary text-black animate-bounce-light'
                  : 'bg-white hover:bg-gray-50 text-black hover-lift'
              }`}
              style={{
                transform: selectedCategory === category.id ? 'rotate(-1deg)' : 'rotate(1deg)',
                animationDelay: `${index * 0.1}s`
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              {category.label} ({category.count})
            </button>
          ))}
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {filteredServices.length > 0 ? (
            filteredServices.map((service, index) => (
              <ServiceCard
                key={service.id}
                service={service}
                index={index}
                isHovered={hoveredCard === service.id}
                onHover={setHoveredCard}
              />
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-lg text-gray-600">No services found for the selected category.</p>
            </div>
          )}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="neo-card p-8 bg-primary max-w-2xl mx-auto rotate-1">
            <h3 className="text-2xl font-bold mb-4">Need Something Custom?</h3>
            <p className="mb-6">
              Don't see exactly what you're looking for? Let's discuss a custom solution 
              tailored specifically for your business needs.
            </p>
            <Link 
              to="/booking"
              className="neo-button bg-white hover:bg-gray-50 text-black px-8 py-3 font-bold inline-flex items-center"
            >
              Get Custom Quote
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

interface ServiceCardProps {
  service: Service;
  index: number;
  isHovered: boolean;
  onHover: (id: string | null) => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, index, isHovered, onHover }) => {
  const IconComponent = service.icon;

  return (
    <Link
      to={`/services/${service.id}`}
      className="block group"
      onMouseEnter={() => onHover(service.id)}
      onMouseLeave={() => onHover(null)}
    >
      <div
        className={`neo-card p-6 bg-white hover-lift cursor-pointer relative overflow-hidden ${
          isHovered ? 'hover-glow animate-wiggle' : ''
        } ${service.popular ? 'animate-pulse-glow' : ''}`}
        style={{
          backgroundColor: service.color,
          transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`,
          animationDelay: `${index * 0.1}s`
        }}
      >
        {/* Popular Badge */}
        {service.popular && (
          <div className="absolute -top-3 -right-3 w-16 h-16">
            <div className="absolute top-0 right-0 w-full h-full bg-accent neo-border rotate-12 flex flex-col items-center justify-center text-white">
              <Star className="h-3 w-3 fill-white" />
              <span className="text-xs font-bold">Popular</span>
            </div>
          </div>
        )}

        {/* Service Icon */}
        <div className="mb-4 text-black">
          <IconComponent size={40} />
        </div>

        {/* Service Info */}
        <h3 className="text-xl font-bold mb-2">{service.title}</h3>
        <p className="text-sm mb-4 line-clamp-2">{service.shortDescription}</p>

        {/* Price and Delivery */}
        <div className="flex justify-between items-center mb-4">
          <span className="font-bold text-lg">
            {formatPrice(service.price, service.currency)}
          </span>
          <span className="text-sm bg-black text-white px-2 py-1 rounded">
            {service.deliveryTime}
          </span>
        </div>

        {/* Service Type Badge */}
        <div className="flex items-center justify-between">
          <span className={`text-xs px-2 py-1 rounded font-bold ${
            service.type === 'one-off' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-blue-100 text-blue-800'
          }`}>
            {service.type === 'one-off' ? 'Buy Now' : 'Request Quote'}
          </span>
          
          <div className="flex items-center text-sm font-bold group-hover:translate-x-1 transition-transform">
            Learn More
            <ArrowRight className="ml-1 h-4 w-4" />
          </div>
        </div>

        {/* Hover Effect */}
        <div className={`absolute inset-0 bg-black bg-opacity-10 transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}></div>
      </div>
    </Link>
  );
};

export default ServicesOverview;
