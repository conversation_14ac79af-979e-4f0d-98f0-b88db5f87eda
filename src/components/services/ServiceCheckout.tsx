import React, { useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import { Service } from '../../data/services';
import { CheckoutProvider } from '../checkout/CheckoutContext';
import ServiceFormSelector from '../checkout/ServiceFormSelector';
import PaymentForm from '../checkout/PaymentForm';

interface ServiceCheckoutProps {
  service: Service;
  onBack: () => void;
}

const ServiceCheckout: React.FC<ServiceCheckoutProps> = ({ service, onBack }) => {
  const [currentStep, setCurrentStep] = useState<'brand-info' | 'payment'>('brand-info');

  return (
    <CheckoutProvider>
      <div className="bg-white rounded-lg border border-gray-200 p-8">
        {/* Header */}
        <div className="flex items-center mb-6">
          <button
            onClick={onBack}
            className="flex items-center text-blue-600 hover:text-blue-700 mr-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Service
          </button>
          <h2 className="text-2xl font-bold text-blue-600">
            Checkout - {service.title}
          </h2>
        </div>

        {/* Checkout Steps */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center ${
              currentStep === 'brand-info' ? 'text-blue-600' : 'text-gray-400'
            }`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-2 ${
                currentStep === 'brand-info' ? 'bg-blue-600 text-white' : 'bg-gray-200'
              }`}>
                1
              </div>
              Project Details
            </div>
            <div className="flex-1 h-px bg-gray-200"></div>
            <div className={`flex items-center ${
              currentStep === 'payment' ? 'text-blue-600' : 'text-gray-400'
            }`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-2 ${
                currentStep === 'payment' ? 'bg-blue-600 text-white' : 'bg-gray-200'
              }`}>
                2
              </div>
              Payment
            </div>
          </div>
        </div>

        {/* Step Content */}
        {currentStep === 'brand-info' ? (
          <ServiceFormSelector
            service={service}
            onNext={() => setCurrentStep('payment')}
          />
        ) : (
          <ServicePaymentForm
            service={service}
            onBack={() => setCurrentStep('brand-info')}
          />
        )}
      </div>
    </CheckoutProvider>
  );
};



// Customized Payment Form for services
interface ServicePaymentFormProps {
  service: Service;
  onBack: () => void;
}

const ServicePaymentForm: React.FC<ServicePaymentFormProps> = ({ service, onBack }) => {
  return (
    <div>
      <h3 className="text-lg font-bold mb-4">Payment Information</h3>
      <p className="text-gray-600 mb-6">
        Complete your purchase for {service.title} - ${typeof service.price === 'number' ? service.price.toLocaleString() : 'Custom'} {service.currency}
      </p>
      <PaymentForm onBack={onBack} service={service} />
    </div>
  );
};

export default ServiceCheckout;
