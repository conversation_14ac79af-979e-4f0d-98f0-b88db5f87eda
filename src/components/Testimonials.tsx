
import React from 'react';

const Testimonials = () => {
  const testimonials = [
    {
      quote: "<PERSON><PERSON>'s work has made our platform a reality. Working with <PERSON><PERSON> is great. He has a brilliant way of mixing marketing and tech.",
      author: "<PERSON>",
      position: "CEO",
      company: "FashionLab"
    },
    {
      quote: "<PERSON><PERSON>'s strategic insights helped us increase our lead generation by 60% in just 3 months. His approach is both practical and innovative.",
      author: "<PERSON>",
      position: "Marketing Director",
      company: "TechStart Inc."
    },
    {
      quote: "The clarity and direction <PERSON><PERSON> provided saved us from costly mistakes. His expertise in scaling marketing efforts is unmatched.",
      author: "<PERSON>",
      position: "Founder",
      company: "GrowthTech Solutions"
    }
  ];

  return (
    <section id="testimonials" className="py-20 bg-muted overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-blue text-white font-bold mb-4 rotate-1">
            Client Success
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            What Clients Say About Working With Me
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="neo-card p-6 bg-white relative"
              style={{ 
                transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
              }}
            >
              {/* Quote mark */}
              <div className="absolute -top-5 -right-2 w-12 h-12 flex items-center justify-center neo-border bg-primary text-3xl font-serif rotate-12">
                "
              </div>
              
              <p className="mb-6 text-lg">{testimonial.quote}</p>
              
              <div>
                <p className="font-bold">{testimonial.author}</p>
                <p className="text-sm">{testimonial.position}, {testimonial.company}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
