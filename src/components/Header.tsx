import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import Interactive<PERSON>ogo from './InteractiveLogo';
import Navbar from './Navbar';
import { ctaButton } from '../data/navigationLinks';
import { useAnalytics } from '../services/analytics';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const analytics = useAnalytics();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 ${
        isScrolled
          ? 'h-16 bg-white neo-border border-t-0 border-l-0 border-r-0'
          : 'h-20 bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 h-full flex justify-between items-center gap-4">
        <div className="brand flex-shrink-0">
          <InteractiveLogo isScrolled={isScrolled} />
        </div>

        <Navbar />

        <div className="hidden lg:block flex-shrink-0">
          <a
            href={ctaButton.href}
            onClick={() => analytics.track('cta_clicked', {
              cta_text: ctaButton.label,
              cta_location: 'header',
              destination: ctaButton.href,
            })}
            className="neo-button bg-primary hover:bg-primary/90 text-sm xl:text-base px-4 xl:px-6"
          >
            <span className="hidden xl:inline">{ctaButton.label}</span>
            <span className="xl:hidden">Book Now</span>
          </a>
        </div>

        <button
          className="block lg:hidden text-black flex-shrink-0"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>
      
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="lg:hidden absolute top-full left-0 right-0 bg-white neo-border border-t-0 border-l-0 border-r-0 py-4 animate-fade-in">
          <div className="container mx-auto px-4">
            <Navbar
              isMobile
              onLinkClick={() => setMobileMenuOpen(false)}
            />
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;