import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAnalytics } from '../../services/analytics';

// Map routes to readable page names
const getPageName = (pathname: string): string => {
  const routeMap: Record<string, string> = {
    '/': 'Home',
    '/about': 'About',
    '/experience': 'Experience',
    '/contact': 'Contact',
    '/booking': 'Booking',
    '/services': 'Services',
    '/success': 'Success',
    '/request-success': 'Request Success',
  };

  // Handle dynamic routes like /services/:serviceId
  if (pathname.startsWith('/services/') && pathname !== '/services') {
    return 'Service Detail';
  }

  return routeMap[pathname] || 'Unknown Page';
};

const PageViewTracker = () => {
  const location = useLocation();
  const analytics = useAnalytics();

  useEffect(() => {
    const pageName = getPageName(location.pathname);
    
    // Track page view with PostHog
    analytics.trackPageView(pageName, {
      search: location.search,
      hash: location.hash,
    });

    // Also track with PostHog's built-in page view tracking
    if (window.posthog) {
      window.posthog.capture('$pageview');
    }
  }, [location, analytics]);

  return null; // This component doesn't render anything
};

export default PageViewTracker;
