
import React from 'react';
import { Lightbulb, Target, Zap, Clock } from 'lucide-react';

const Benefits = () => {
  const benefits = [
    {
      icon: <Lightbulb size={40} />,
      title: "Strategic Expertise",
      description: "Leverage years of hands-on marketing experience across startups, scale-ups, and enterprise companies."
    },
    {
      icon: <Target size={40} />,
      title: "Tailored Approach",
      description: "Every strategy is customized to your business stage, industry, and specific growth challenges."
    },
    {
      icon: <Zap size={40} />,
      title: "Actionable Insights",
      description: "Walk away with clear, prioritized action items you can implement immediately to drive results."
    },
    {
      icon: <Clock size={40} />,
      title: "Efficient Process",
      description: "Get strategic clarity quickly without lengthy consulting engagements or unnecessary complexity."
    }
  ];

  return (
    <section id="about" className="py-20 bg-muted overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 rotate-1">
            Why Work With Me
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            Strategic Marketing Expertise
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className="neo-card p-6 bg-white hover:-translate-y-2 cursor-pointer"
              style={{
                backgroundColor: index % 4 === 0 ? '#FFFFFF' :
                               index % 4 === 1 ? '#FFC107' :
                               index % 4 === 2 ? '#2196F3' :
                               '#FF3366',
                transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
              }}
            >
              <div className="mb-4 text-black">{benefit.icon}</div>
              <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
              <p>{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Benefits;
