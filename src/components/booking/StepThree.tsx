import React from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Checkbox } from '../ui/checkbox';
import MultiSelect from '../ui/multi-select';

const MARKETING_AREAS = [
  { value: 'paid-advertising', label: 'Paid advertising' },
  { value: 'growth-strategy', label: 'Growth strategy' },
  { value: 'content-marketing', label: 'Content marketing' },
  { value: 'seo', label: 'Search engine optimization' },
  { value: 'email-marketing', label: 'Email marketing' },
  { value: 'offline-marketing', label: 'Offline marketing' },
  { value: 'web-design', label: 'Web design' },
  { value: 'web-development', label: 'Web development' },
  { value: 'web-copy', label: 'Web copy' },
  { value: 'creative-production', label: 'Creative production' },
  { value: 'cro', label: 'CRO/conversion optimization' },
  { value: 'copywriting', label: 'Copywriting' },
  { value: 'social-media', label: 'Social media management' },
  { value: 'influencer-marketing', label: 'Influencer marketing' },
  { value: 'brand-strategy', label: 'Brand strategy' },
  { value: 'crm-analytics', label: 'CRM analytics' },
  { value: 'product-design', label: 'Product design' },
  { value: 'app-marketing', label: 'App marketing' },
  { value: 'marketing-automation', label: 'Marketing automation' },
  { value: 'personal-branding', label: 'Personal branding' },
];

const DECISION_MAKERS = [
  { id: 'just_me', label: 'Just me' },
  { id: 'me_and_team', label: 'Me and other team members' },
  { id: 'other_team', label: 'Other team members' },
];

const StepThree = () => {
  const { formData, updateFormData, errors } = useFormContext();

  const handleRadioChange = (field: string, value: string) => {
    updateFormData({ [field]: value });
    
    // Clear related fields when runningAds changes to 'no'
    if (field === 'runningAds' && value === 'no') {
      updateFormData({ 
        adsEffective: '',
        adBudget: '' 
      });
    }
  };

  const handleCheckboxChange = (field: string, id: string) => {
    const currentValues = formData[field as keyof typeof formData] as string[];
    
    if (Array.isArray(currentValues)) {
      const updatedValues = currentValues.includes(id)
        ? currentValues.filter(v => v !== id)
        : [...currentValues, id];
      
      updateFormData({ [field]: updatedValues });
    }
  };

  const handleMarketingAreasChange = (selectedValues: string[]) => {
    updateFormData({ marketingAreas: selectedValues });
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-2">Now let's learn more about your marketing situation</h2>
      <p className="mb-6">Tell us where you are and what you need help with.</p>
      
      <div className="space-y-6">
        <div>
          <Label className="block mb-3 font-medium">
            Are you currently running ads?
          </Label>
          <RadioGroup
            value={formData.runningAds}
            onValueChange={(value) => handleRadioChange('runningAds', value)}
            className="space-y-2"
          >
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="yes" id="running_ads_yes" />
              <Label htmlFor="running_ads_yes">Yes</Label>
            </div>
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="no" id="running_ads_no" />
              <Label htmlFor="running_ads_no">No</Label>
            </div>
          </RadioGroup>
          {errors.runningAds && (
            <p className="mt-1 text-red-500 text-sm">{errors.runningAds}</p>
          )}
        </div>
        
        {formData.runningAds === 'yes' && (
          <>
            <div>
              <Label className="block mb-3 font-medium">
                Are they effective/profitable?
              </Label>
              <RadioGroup
                value={formData.adsEffective || ''}
                onValueChange={(value) => handleRadioChange('adsEffective', value)}
                className="space-y-2"
              >
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="yes" id="ads_effective_yes" />
                  <Label htmlFor="ads_effective_yes">Yes</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="no" id="ads_effective_no" />
                  <Label htmlFor="ads_effective_no">No</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="not_sure" id="ads_effective_not_sure" />
                  <Label htmlFor="ads_effective_not_sure">Not sure</Label>
                </div>
              </RadioGroup>
              {errors.adsEffective && (
                <p className="mt-1 text-red-500 text-sm">{errors.adsEffective}</p>
              )}
            </div>
            
            <div>
              <Label className="block mb-3 font-medium">
                What's your estimated monthly ad budget?
              </Label>
              <p className="text-sm text-muted-foreground mb-3">
                Note: Clients spending $5k+/month typically get the best results with our model
              </p>
              <RadioGroup
                value={formData.adBudget}
                onValueChange={(value) => handleRadioChange('adBudget', value)}
                className="space-y-2"
              >
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="less_than_5k" id="less_than_5k" />
                  <Label htmlFor="less_than_5k">Less than $5k/mo</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="5k_10k" id="5k_10k" />
                  <Label htmlFor="5k_10k">$5k-$10k/mo</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="10k_20k" id="10k_20k" />
                  <Label htmlFor="10k_20k">$10k-$20k/mo</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="20k_50k" id="20k_50k" />
                  <Label htmlFor="20k_50k">$20k-$50k/mo</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="50k_100k" id="50k_100k" />
                  <Label htmlFor="50k_100k">$50k-$100k/mo</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="100k_plus" id="100k_plus" />
                  <Label htmlFor="100k_plus">$100k+/mo</Label>
                </div>
              </RadioGroup>
              {errors.adBudget && (
                <p className="mt-1 text-red-500 text-sm">{errors.adBudget}</p>
              )}
            </div>
          </>
        )}
        
        <div>
          <Label className="block mb-3 font-medium">
            Are you currently managing marketing in-house or through an agency?
          </Label>
          <RadioGroup
            value={formData.marketingManagement}
            onValueChange={(value) => handleRadioChange('marketingManagement', value)}
            className="space-y-2"
          >
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="in_house" id="in_house" />
              <Label htmlFor="in_house">In-house</Label>
            </div>
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="agency" id="agency" />
              <Label htmlFor="agency">Agency/freelancer</Label>
            </div>
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="mix" id="mix" />
              <Label htmlFor="mix">Mix of both</Label>
            </div>
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="not_managed" id="not_managed" />
              <Label htmlFor="not_managed">Not currently managed</Label>
            </div>
          </RadioGroup>
          {errors.marketingManagement && (
            <p className="mt-1 text-red-500 text-sm">{errors.marketingManagement}</p>
          )}
        </div>
        
        <div>
          <Label className="block mb-3 font-medium">
            Who will be involved in the decision-making process?
          </Label>
          <div className="space-y-2">
            {DECISION_MAKERS.map((option) => (
              <div key={option.id} className="flex items-start space-x-2 neo-border p-3">
                <Checkbox 
                  id={option.id}
                  checked={formData.decisionMakers.includes(option.id)}
                  onCheckedChange={() => handleCheckboxChange('decisionMakers', option.id)}
                />
                <Label htmlFor={option.id}>{option.label}</Label>
              </div>
            ))}
          </div>
          {errors.decisionMakers && (
            <p className="mt-1 text-red-500 text-sm">{errors.decisionMakers}</p>
          )}
        </div>
        
        <div>
          <Label className="block mb-3 font-medium">
            What's your timeline for implementing changes?
          </Label>
          <RadioGroup
            value={formData.implementationTimeline}
            onValueChange={(value) => handleRadioChange('implementationTimeline', value)}
            className="space-y-2"
          >
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="right_now" id="right_now" />
              <Label htmlFor="right_now">I need help right now</Label>
            </div>
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="1_3_months" id="1_3_months" />
              <Label htmlFor="1_3_months">In the next 1-3 months</Label>
            </div>
            <div className="flex items-center space-x-2 neo-border p-3">
              <RadioGroupItem value="exploring" id="exploring" />
              <Label htmlFor="exploring">Just exploring options</Label>
            </div>
          </RadioGroup>
          {errors.implementationTimeline && (
            <p className="mt-1 text-red-500 text-sm">{errors.implementationTimeline}</p>
          )}
        </div>
        
        <div>
          <Label className="block mb-3 font-medium">
            Which areas do you need the most help with?
          </Label>
          <MultiSelect 
            options={MARKETING_AREAS}
            selectedValues={formData.marketingAreas}
            onChange={handleMarketingAreasChange}
            placeholder="Select marketing areas..."
          />
          {errors.marketingAreas && (
            <p className="mt-1 text-red-500 text-sm">{errors.marketingAreas}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StepThree;
