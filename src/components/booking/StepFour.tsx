
import React from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Textarea } from '../ui/textarea';

const MATERIALS = [
  { id: 'analytics', label: 'Analytics access' },
  { id: 'campaign_data', label: 'Previous campaign data' },
  { id: 'brand_guidelines', label: 'Brand guidelines' },
  { id: 'marketing_strategy', label: 'Marketing strategy documents' },
  { id: 'other', label: 'Other' },
];

const StepFour = () => {
  const { formData, updateFormData, errors } = useFormContext();

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
  };

  const handleCheckboxChange = (field: string, id: string) => {
    const currentValues = formData[field as keyof typeof formData] as string[];
    
    if (Array.isArray(currentValues)) {
      const updatedValues = currentValues.includes(id)
        ? currentValues.filter(v => v !== id)
        : [...currentValues, id];
      
      updateFormData({ [field]: updatedValues });
    }
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-2">Get the most from your Marketing Therapy</h2>
      <p className="mb-6">Your marketing doctor needs some info to prescribe the right remedies!</p>
      
      <div className="space-y-6">
        <div>
          <Label htmlFor="marketingChallenges" className="block mb-2 font-medium">
            What marketing headaches keep you up at night? (Top 3)
          </Label>
          <Textarea 
            id="marketingChallenges"
            name="marketingChallenges"
            value={formData.marketingChallenges}
            onChange={handleTextChange}
            rows={4}
            className={`neo-input w-full ${errors.marketingChallenges ? 'border-red-500' : ''}`}
            placeholder="E.g., High CAC, low conversion rates, unclear positioning..."
          />
          {errors.marketingChallenges && (
            <p className="mt-1 text-red-500 text-sm">{errors.marketingChallenges}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="sessionOutcomes" className="block mb-2 font-medium">
            What specific outcomes are you hoping for from this strategy session?
          </Label>
          <Textarea 
            id="sessionOutcomes"
            name="sessionOutcomes"
            value={formData.sessionOutcomes}
            onChange={handleTextChange}
            rows={4}
            className={`neo-input w-full ${errors.sessionOutcomes ? 'border-red-500' : ''}`}
            placeholder="E.g., Create a better ad strategy, improve my landing page conversion rate..."
          />
          {errors.sessionOutcomes && (
            <p className="mt-1 text-red-500 text-sm">{errors.sessionOutcomes}</p>
          )}
        </div>
        
        <div>
          <Label className="block mb-3 font-medium">
            Any marketing medical records you can share with us beforehand?
          </Label>
          <div className="space-y-2">
            {MATERIALS.map((option) => (
              <div key={option.id} className="flex items-start space-x-2 neo-border p-3">
                <Checkbox 
                  id={option.id}
                  checked={formData.materialsToShare.includes(option.id)}
                  onCheckedChange={() => handleCheckboxChange('materialsToShare', option.id)}
                />
                <Label htmlFor={option.id}>{option.label}</Label>
              </div>
            ))}
          </div>
          
          {formData.materialsToShare.includes('other') && (
            <div className="mt-3">
              <Label htmlFor="otherMaterials" className="block mb-1 font-medium">
                Please specify what other materials you can share
              </Label>
              <input 
                type="text"
                id="otherMaterials"
                name="otherMaterials"
                value={formData.otherMaterials || ''}
                onChange={handleInputChange}
                className="neo-input w-full"
              />
            </div>
          )}
        </div>
        
        <div>
          <Label htmlFor="additionalInfo" className="block mb-2 font-medium">
            Any other marketing symptoms we should know about?
          </Label>
          <Textarea 
            id="additionalInfo"
            name="additionalInfo"
            value={formData.additionalInfo}
            onChange={handleTextChange}
            rows={4}
            className="neo-input w-full"
            placeholder="Any additional context or questions..."
          />
        </div>
        
        <div className="border-t-2 border-black pt-4">
          <div className="neo-border p-4 bg-white -rotate-1 text-center">
            <p className="font-medium text-primary">After booking, you'll be redirected to complete your payment</p>
            <p className="text-sm text-muted-foreground">Then you'll receive a calendar invite for your strategic consultation!</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepFour;
