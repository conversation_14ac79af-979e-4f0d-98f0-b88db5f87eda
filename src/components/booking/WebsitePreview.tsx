
import React from 'react';
import { ExternalLink } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export interface WebsiteMetadata {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  siteName?: string;
  favicon?: string;
}

interface WebsitePreviewProps {
  metadata: WebsiteMetadata | null;
  isLoading: boolean;
  error?: string;
}

const WebsitePreview: React.FC<WebsitePreviewProps> = ({ metadata, isLoading, error }) => {
  if (isLoading) {
    return (
      <Card className="neo-card overflow-hidden mt-2">
        <CardContent className="p-3">
          <div className="flex items-start space-x-3">
            <Skeleton className="h-12 w-12 rounded-md flex-shrink-0" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return null; // Don't show anything on error
  }

  if (!metadata || !metadata.title) {
    return null;
  }

  return (
    <Card className="neo-card overflow-hidden mt-2 website-preview">
      <CardContent className="p-3">
        <div className="flex items-start space-x-3">
          {metadata.favicon && (
            <div className="h-12 w-12 rounded-md flex-shrink-0 bg-muted flex items-center justify-center overflow-hidden">
              <img 
                src={metadata.favicon} 
                alt={metadata.siteName || metadata.title} 
                className="h-10 w-10 object-contain"
                onError={(e) => {
                  // Hide on error
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          )}
          
          <div className="flex-1">
            <h4 className="font-medium text-sm line-clamp-1">{metadata.title}</h4>
            {metadata.description && (
              <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                {metadata.description}
              </p>
            )}
            <div className="flex items-center mt-1">
              <a 
                href={metadata.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-primary flex items-center hover:underline"
              >
                {metadata.url?.replace(/(^\w+:|^)\/\//, '').replace(/\/$/, '')}
                <ExternalLink size={10} className="ml-1" />
              </a>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default WebsitePreview;
