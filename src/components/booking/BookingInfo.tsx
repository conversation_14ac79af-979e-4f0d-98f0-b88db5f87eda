
import React from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { CalendarDays, Clock, CheckCircle, DollarSign } from 'lucide-react';

const BookingInfo = () => {
  const { currentStep } = useFormContext();
  
  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-4">Strategic Marketing Consultation</h2>
      
      {currentStep === 1 && (
        <div>
          <div className="aspect-video neo-border mb-6 flex items-center justify-center bg-muted">
            <p className="text-center text-muted-foreground">
              Video introduction placeholder
              <br />
              <span className="text-sm">(Future: Video explaining the consultation process)</span>
            </p>
          </div>
          
          <p className="mb-4">
            My strategic marketing consultations are designed to give you actionable
            insights and solve your most pressing marketing challenges in just 30 minutes.
          </p>
          
          <div className="space-y-4 mt-6">
            <div className="flex items-center gap-3">
              <CalendarDays className="text-primary" />
              <span>One-on-one session with a marketing expert</span>
            </div>
            <div className="flex items-center gap-3">
              <Clock className="text-primary" />
              <span>30 minutes of focused problem-solving</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="text-primary" />
              <span>Personalized advice for your specific challenges</span>
            </div>
            <div className="flex items-center gap-3">
              <DollarSign className="text-primary" />
              <span>$197 per session</span>
            </div>
          </div>
        </div>
      )}
      
      {currentStep === 2 && (
        <div>
          <h3 className="text-xl font-bold mb-3">Why we ask about your business</h3>
          <p className="mb-4">
            Understanding your business context helps me provide tailored strategic advice
            for your specific situation. Different industries and business stages require
            different marketing approaches and priorities.
          </p>

          <div className="neo-border p-4 mb-4 bg-primary/10">
            <h4 className="font-bold mb-2">Consultation Focus</h4>
            <p>
              My strategic consultations are designed to provide maximum value for businesses that:
            </p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Are ready to scale their marketing efforts</li>
              <li>Need strategic direction and expert guidance</li>
              <li>Want actionable insights they can implement immediately</li>
            </ul>
          </div>
          
          <div className="aspect-video neo-border mb-6 flex items-center justify-center bg-muted">
            <p className="text-center text-muted-foreground">
              Video about our approach
              <br />
              <span className="text-sm">(Future: Video explaining our methodology)</span>
            </p>
          </div>
        </div>
      )}
      
      {currentStep === 3 && (
        <div>
          <h3 className="text-xl font-bold mb-3">My Marketing Expertise</h3>
          <p className="mb-4">
            I have helped businesses across various marketing disciplines.
            By understanding your specific marketing challenges, I can provide
            targeted expertise for your session.
          </p>
          
          <div className="grid grid-cols-2 gap-3 mb-6">
            <div className="neo-border p-3 bg-secondary/10">
              <h4 className="font-bold">Acquisition</h4>
              <p className="text-sm">Paid ads, SEO, content</p>
            </div>
            <div className="neo-border p-3 bg-primary/10">
              <h4 className="font-bold">Conversion</h4>
              <p className="text-sm">CRO, landing pages</p>
            </div>
            <div className="neo-border p-3 bg-accent/10">
              <h4 className="font-bold">Retention</h4>
              <p className="text-sm">Email, loyalty, CRM</p>
            </div>
            <div className="neo-border p-3 bg-secondary/10">
              <h4 className="font-bold">Strategy</h4>
              <p className="text-sm">Growth, positioning</p>
            </div>
          </div>
          
          <div className="neo-border p-4 bg-muted">
            <h4 className="font-bold mb-2">Client Success Story</h4>
            <p className="text-sm italic">
              "After just one session, we completely revamped our ad strategy and saw a 43% 
              decrease in CAC within two weeks. Best money we've spent on marketing advice."
            </p>
            <p className="text-sm font-bold mt-2">— Sarah J., SaaS Founder</p>
          </div>
        </div>
      )}
      
      {currentStep === 4 && (
        <div>
          <h3 className="text-xl font-bold mb-3">How to Prepare for Your Session</h3>
          <p className="mb-4">
            The more information you can share with me beforehand, the more value
            I can provide during your 30-minute session.
          </p>
          
          <div className="space-y-4 mb-6">
            <div className="neo-border p-3">
              <h4 className="font-bold">1. Come with specific questions</h4>
              <p className="text-sm">
                The more specific your questions, the more actionable our advice will be.
              </p>
            </div>
            <div className="neo-border p-3">
              <h4 className="font-bold">2. Share relevant data</h4>
              <p className="text-sm">
                Analytics, campaign results, or other marketing metrics help us understand your current situation.
              </p>
            </div>
            <div className="neo-border p-3">
              <h4 className="font-bold">3. Be ready to take notes</h4>
              <p className="text-sm">
                I'll cover a lot in 30 minutes - come prepared to capture key insights.
              </p>
            </div>
          </div>
          
          <div className="neo-border p-4 bg-primary/10">
            <h4 className="font-bold mb-2">What Happens Next?</h4>
            <p className="text-sm">
              After submitting this form, you'll select an available time slot and complete
              payment. You'll receive a calendar invite with a Zoom link for your session.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingInfo;
