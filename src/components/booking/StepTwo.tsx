
import React, { useState } from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Input } from '../ui/input';
import { Check, Search, X } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '../ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Button } from '../ui/button';
import { cn } from '@/lib/utils';

const INDUSTRIES = [
  { value: 'ecommerce', label: 'E-commerce' },
  { value: 'saas', label: 'SaaS' },
  { value: 'finance', label: 'Finance' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'education', label: 'Education' },
  { value: 'travel', label: 'Travel' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'nonprofit', label: 'Non-profit' },
  { value: 'entertainment', label: 'Entertainment' },
  { value: 'food_beverage', label: 'Food & Beverage' },
  { value: 'retail', label: 'Retail' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'technology', label: 'Technology' },
  { value: 'professional_services', label: 'Professional Services' },
  { value: 'other', label: 'Other' }
];

const BUSINESS_TYPES = [
  { value: 'startup', label: 'Startup' },
  { value: 'established_business', label: 'Established Business' },
  { value: 'agency', label: 'Agency' },
  { value: 'freelancer', label: 'Freelancer/Consultant' },
  { value: 'other', label: 'Other' }
];

const StepTwo = () => {
  const { formData, updateFormData, errors } = useFormContext();
  const [customIndustry, setCustomIndustry] = useState('');
  const [industryPopoverOpen, setIndustryPopoverOpen] = useState(false);

  const handleRadioChange = (field: string, value: string) => {
    updateFormData({ [field]: value });
    
    // Reset related fields when business type changes
    if (field === 'businessType' && value !== 'startup') {
      updateFormData({ 
        fundingStatus: '',
        businessStage: ''
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
  };

  const handleCustomIndustryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomIndustry(e.target.value);
  };

  const addCustomIndustry = () => {
    if (customIndustry.trim()) {
      updateFormData({ 
        industry: 'custom',
        otherIndustry: customIndustry 
      });
      setCustomIndustry('');
      setIndustryPopoverOpen(false);
    }
  };

  const handleIndustrySelect = (value: string) => {
    updateFormData({ industry: value });
    setIndustryPopoverOpen(false);
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-2">Tell us about your business</h2>
      <p className="mb-6">This helps us tailor our advice to your specific situation.</p>
      
      <div className="space-y-6">
        <div>
          <Label className="block mb-3 font-medium">
            What type of business are you?
          </Label>
          <RadioGroup
            value={formData.businessType}
            onValueChange={(value) => handleRadioChange('businessType', value)}
            className="space-y-2"
          >
            {BUSINESS_TYPES.map(type => (
              <div key={type.value} className="flex items-center space-x-2 neo-border p-3">
                <RadioGroupItem value={type.value} id={`business_type_${type.value}`} />
                <Label htmlFor={`business_type_${type.value}`}>{type.label}</Label>
              </div>
            ))}
          </RadioGroup>
          {errors.businessType && (
            <p className="mt-1 text-red-500 text-sm">{errors.businessType}</p>
          )}
        </div>
        
        {/* Show startup-specific questions only if business type is startup */}
        {formData.businessType === 'startup' && (
          <>
            <div>
              <Label className="block mb-3 font-medium">
                Are you bootstrapped or have you raised funds?
              </Label>
              <RadioGroup
                value={formData.fundingStatus}
                onValueChange={(value) => handleRadioChange('fundingStatus', value)}
                className="space-y-2"
              >
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="bootstrapped" id="bootstrapped" />
                  <Label htmlFor="bootstrapped">Bootstrapped</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="funded" id="funded" />
                  <Label htmlFor="funded">Funded</Label>
                </div>
              </RadioGroup>
              {errors.fundingStatus && (
                <p className="mt-1 text-red-500 text-sm">{errors.fundingStatus}</p>
              )}
            </div>
            
            <div>
              <Label className="block mb-3 font-medium">
                What's your current stage?
              </Label>
              <RadioGroup
                value={formData.businessStage}
                onValueChange={(value) => handleRadioChange('businessStage', value)}
                className="space-y-2"
              >
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="early_stage" id="early_stage" />
                  <Label htmlFor="early_stage">Early-stage startup</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="growth_stage" id="growth_stage" />
                  <Label htmlFor="growth_stage">Growth-stage company</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="established" id="established" />
                  <Label htmlFor="established">Established business</Label>
                </div>
                <div className="flex items-center space-x-2 neo-border p-3">
                  <RadioGroupItem value="enterprise" id="enterprise" />
                  <Label htmlFor="enterprise">Enterprise</Label>
                </div>
              </RadioGroup>
              {errors.businessStage && (
                <p className="mt-1 text-red-500 text-sm">{errors.businessStage}</p>
              )}
            </div>
          </>
        )}
        
        <div>
          <Label htmlFor="industry" className="block mb-2 font-medium">
            What industry are you in?
          </Label>
          <Popover open={industryPopoverOpen} onOpenChange={setIndustryPopoverOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={industryPopoverOpen}
                className="w-full justify-between neo-input"
              >
                {formData.industry ? (
                  formData.industry === 'custom' ? 
                  formData.otherIndustry : 
                  INDUSTRIES.find(industry => industry.value === formData.industry)?.label
                ) : (
                  "Select or search for your industry"
                )}
                <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0" align="start">
              <Command>
                <CommandInput placeholder="Search industry..." />
                <CommandList>
                  <CommandEmpty>
                    <div className="flex flex-col space-y-2 p-2">
                      <p>No industry found. Add your own:</p>
                      <div className="flex space-x-2">
                        <Input
                          value={customIndustry}
                          onChange={handleCustomIndustryChange}
                          placeholder="Enter your industry"
                          className="flex-1"
                        />
                        <Button onClick={addCustomIndustry} size="sm">Add</Button>
                      </div>
                    </div>
                  </CommandEmpty>
                  <CommandGroup>
                    {INDUSTRIES.map(industry => (
                      <CommandItem
                        key={industry.value}
                        value={industry.value}
                        onSelect={() => handleIndustrySelect(industry.value)}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            formData.industry === industry.value ? "opacity-100" : "opacity-0"
                          )}
                        />
                        {industry.label}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                  <CommandGroup heading="Custom">
                    <div className="flex space-x-2 p-2">
                      <Input
                        value={customIndustry}
                        onChange={handleCustomIndustryChange}
                        placeholder="Enter your industry"
                        className="flex-1"
                      />
                      <Button onClick={addCustomIndustry} size="sm">Add</Button>
                    </div>
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          {errors.industry && (
            <p className="mt-1 text-red-500 text-sm">{errors.industry}</p>
          )}
          
          {formData.industry === 'other' && (
            <div className="mt-3">
              <Label htmlFor="otherIndustry" className="block mb-1 font-medium">
                Please specify your industry
              </Label>
              <Input 
                type="text"
                id="otherIndustry"
                name="otherIndustry"
                value={formData.otherIndustry || ''}
                onChange={handleInputChange}
                className={`neo-input w-full ${errors.otherIndustry ? 'border-red-500' : ''}`}
              />
              {errors.otherIndustry && (
                <p className="mt-1 text-red-500 text-sm">{errors.otherIndustry}</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StepTwo;
