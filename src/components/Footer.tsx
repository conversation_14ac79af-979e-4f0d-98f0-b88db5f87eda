
import React from 'react';
import { useAnalytics } from '../services/analytics';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const analytics = useAnalytics();
  
  return (
    <footer className="py-12 neo-border border-b-0 border-l-0 border-r-0">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <h3 className="text-xl font-bold mb-4">Asger.me</h3>
            <p className="mb-4">Strategic marketing consultant helping startups and scale-ups achieve sustainable growth through data-driven strategies and expert guidance.</p>
            <div className="flex space-x-4">
              <a href="https://linkedin.com/in/asger" className="hover:text-primary transition-colors" target="_blank" rel="noopener noreferrer">LinkedIn</a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">Services</h3>
            <ul className="space-y-2">
              <li><a href="/services" className="hover:text-primary transition-colors">All Services</a></li>
              <li><a href="/about" className="hover:text-primary transition-colors">About</a></li>
              <li><a href="/experience" className="hover:text-primary transition-colors">Experience</a></li>
              <li><a href="/contact" className="hover:text-primary transition-colors">Contact</a></li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">Get in Touch</h3>
            <p className="mb-2">Ready to discuss your marketing strategy?</p>
            <a
              href="/booking"
              onClick={() => analytics.track('cta_clicked', {
                cta_text: 'Book Strategy Call',
                cta_location: 'footer',
                destination: '/booking',
              })}
              className="neo-button bg-primary hover:bg-primary/90 text-black text-sm px-4 py-2 inline-block"
            >
              Book Strategy Call
            </a>
          </div>
        </div>

        <div className="mt-12 pt-6 border-t-2 border-black">
          <p className="text-center">
            © {currentYear} Asger Teglgaard. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
