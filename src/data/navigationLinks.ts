export interface NavigationLink {
  href: string;
  label: string;
  external?: boolean;
}

export const navigationLinks: NavigationLink[] = [
  { href: '/', label: 'Home' },
  { href: '/services', label: 'Services' },
  { href: '/about', label: 'About' },
  { href: '/experience', label: 'Experience' },
  { href: '/#testimonials', label: 'Testimonials' },
  { href: '/contact', label: 'Contact' },
];

export const ctaButton = {
  href: '/booking',
  label: 'Book Strategy Call',
};
