import { 
  Palette, 
  Code, 
  Smartphone, 
  Search, 
  BarChart3, 
  Rocket,
  Globe,
  ShoppingCart,
  Users,
  Mic,
  BookOpen,
  Target,
  Zap,
  TrendingUp
} from 'lucide-react';

export type ServiceType = 'one-off' | 'consultation';

export interface Service {
  id: string;
  title: string;
  shortDescription: string;
  fullDescription: string;
  price: number | 'custom';
  currency: string;
  deliveryTime: string;
  type: ServiceType;
  category: 'design' | 'development' | 'marketing' | 'consulting';
  icon: any;
  features: string[];
  includes: string[];
  process: string[];
  testimonial?: {
    quote: string;
    author: string;
    company: string;
    rating: number;
  };
  gallery?: string[];
  popular?: boolean;
  color: string;
}

export const services: Service[] = [
  {
    id: 'marketing-strategy-consultation',
    title: 'Marketing Strategy Consultation',
    shortDescription: 'Strategic marketing guidance tailored to your business goals',
    fullDescription: 'Get expert marketing strategy consultation from an experienced professional who has helped scale multiple businesses. I\'ll analyze your current approach and provide actionable recommendations.',
    price: 299,
    currency: 'USD',
    deliveryTime: '60 minutes',
    type: 'consultation',
    category: 'consulting',
    icon: Target,
    color: '#FFC107',
    popular: true,
    features: [
      'Strategic marketing analysis',
      'Personalized action plan',
      'Competitive landscape review',
      'Growth opportunity identification',
      'Implementation roadmap'
    ],
    includes: [
      '60-minute video consultation',
      'Pre-session business analysis',
      'Written strategy summary',
      'Action plan with priorities',
      '2-week follow-up email'
    ],
    process: [
      'Submit business information form',
      'I review your current marketing',
      'Strategic consultation session',
      'Receive written recommendations',
      'Follow-up support via email'
    ],
    testimonial: {
      quote: "Asger's strategic insights helped us increase our lead generation by 60% in just 3 months!",
      author: "Marcus Johnson",
      company: "GrowthTech Solutions",
      rating: 5
    }
  },
  {
    id: 'growth-strategy-intensive',
    title: 'Growth Strategy Intensive',
    shortDescription: 'Deep-dive session to unlock your next growth phase',
    fullDescription: 'A comprehensive 3-hour intensive session where we map out your growth strategy, identify bottlenecks, and create a detailed action plan for scaling your business.',
    price: 799,
    currency: 'USD',
    deliveryTime: '3 hours',
    type: 'consultation',
    category: 'consulting',
    icon: TrendingUp,
    color: '#2196F3',
    features: [
      'Growth opportunity mapping',
      'Bottleneck identification',
      'Channel optimization strategy',
      'Scaling roadmap creation',
      'Implementation timeline'
    ],
    includes: [
      '3-hour intensive session',
      'Pre-session business analysis',
      'Growth strategy document',
      'Action plan with timelines',
      '30-day implementation support'
    ],
    process: [
      'Business assessment questionnaire',
      'Pre-session analysis',
      'Intensive strategy session',
      'Strategy documentation',
      'Implementation kickoff'
    ]
  },
  {
    id: 'marketing-mentorship',
    title: 'Marketing Mentorship Program',
    shortDescription: 'Ongoing guidance to accelerate your marketing skills',
    fullDescription: 'A structured mentorship program designed for marketing professionals and entrepreneurs who want to accelerate their growth with personalized guidance and accountability.',
    price: 1999,
    currency: 'USD',
    deliveryTime: '3 months',
    type: 'consultation',
    category: 'consulting',
    icon: BookOpen,
    color: '#9C27B0',
    features: [
      'Monthly 1-on-1 sessions',
      'Personalized learning path',
      'Real-time feedback',
      'Resource library access',
      'Community network'
    ],
    includes: [
      '3 monthly strategy sessions',
      'Email support between sessions',
      'Custom learning materials',
      'Progress tracking',
      'Certificate of completion'
    ],
    process: [
      'Initial assessment and goal setting',
      'Personalized curriculum creation',
      'Monthly mentorship sessions',
      'Progress reviews and adjustments',
      'Graduation and next steps'
    ]
  },
  {
    id: 'marketing-workshop',
    title: 'Team Marketing Workshop',
    shortDescription: 'Intensive workshop to align and empower your marketing team',
    fullDescription: 'A hands-on workshop where I work directly with your team to develop a cohesive marketing strategy, align on priorities, and build internal capabilities for sustained growth.',
    price: 'custom',
    currency: 'USD',
    deliveryTime: '1-2 days',
    type: 'consultation',
    category: 'consulting',
    icon: Users,
    color: '#2196F3',
    features: [
      'Team strategy alignment',
      'Hands-on framework building',
      'Skill development sessions',
      'Custom playbook creation',
      'Ongoing support included'
    ],
    includes: [
      'Pre-workshop team assessment',
      'Full-day facilitated session',
      'Custom marketing playbook',
      'Team action plan',
      '60-day implementation support'
    ],
    process: [
      'Initial consultation call',
      'Pre-workshop preparation',
      'Workshop facilitation',
      'Strategy documentation',
      'Implementation planning'
    ]
  },
  {
    id: 'public-speaking',
    title: 'Keynote Speaking',
    shortDescription: 'Inspiring talks on marketing strategy and business growth',
    fullDescription: 'Book me for your next conference or corporate event. I deliver engaging, actionable presentations on marketing strategy, growth tactics, and entrepreneurial mindset that inspire and educate your audience.',
    price: 'custom',
    currency: 'USD',
    deliveryTime: 'Event date',
    type: 'consultation',
    category: 'consulting',
    icon: Mic,
    color: '#4CAF50',
    features: [
      'Customized presentation content',
      'Interactive audience engagement',
      'Real-world case studies',
      'Actionable takeaways',
      'Professional delivery'
    ],
    includes: [
      'Pre-event consultation',
      'Custom presentation development',
      'Professional speaking engagement',
      'Audience resource packet',
      'Post-event follow-up'
    ],
    process: [
      'Event brief and audience analysis',
      'Content customization',
      'Presentation rehearsal',
      'Event delivery',
      'Follow-up and feedback collection'
    ]
  },
  {
    id: 'marketing-audit',
    title: 'Marketing Performance Audit',
    shortDescription: 'Deep-dive analysis of your marketing with strategic recommendations',
    fullDescription: 'I\'ll conduct a comprehensive audit of your marketing efforts, analyzing what\'s working, what isn\'t, and where the biggest opportunities lie for growth.',
    price: 1299,
    currency: 'USD',
    deliveryTime: '1-2 weeks',
    type: 'one-off',
    category: 'marketing',
    icon: BarChart3,
    color: '#FF5722',
    features: [
      'Complete marketing stack analysis',
      'Competitive positioning review',
      'Performance metrics deep-dive',
      'Strategic recommendations',
      'Prioritized action roadmap'
    ],
    includes: [
      'Comprehensive audit report',
      'Executive summary presentation',
      'Quick-win recommendations',
      '60-minute results call',
      'Implementation templates'
    ],
    process: [
      'Access setup and data collection',
      'Multi-channel analysis',
      'Competitive research',
      'Report creation and review',
      'Results presentation and Q&A'
    ]
  }
];

export const getServiceById = (id: string): Service | undefined => {
  return services.find(service => service.id === id);
};

export const getServicesByCategory = (category: string): Service[] => {
  return services.filter(service => service.category === category);
};

export const getServicesByType = (type: ServiceType): Service[] => {
  return services.filter(service => service.type === type);
};
