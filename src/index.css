
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 0 0% 10%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;

    --primary: 47 96% 53%;
    --primary-foreground: 0 0% 10%;

    --secondary: 220 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --accent: 339 100% 60%;
    --accent-foreground: 0 0% 100%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 0%;
    --input: 0 0% 0%;
    --ring: 0 0% 0%;

    --radius: 0rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  .neo-border {
    @apply border-[3px] border-black;
  }

  .neo-shadow {
    @apply shadow-[4px_4px_0px_0px_rgba(0,0,0,1)];
  }

  .neo-card {
    @apply neo-border neo-shadow bg-white transition-all duration-300;
  }

  .neo-button {
    @apply neo-border neo-shadow px-6 py-3 font-bold transition-all duration-200 active:translate-x-[2px] active:translate-y-[2px] active:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,1)];
  }

  .neo-input {
    @apply neo-border bg-white px-4 py-3 focus:outline-none focus:ring-4 ring-primary/50;
  }

  .step-enter {
    @apply opacity-0 translate-x-4;
  }

  .step-enter-active {
    @apply opacity-100 translate-x-0 transition-all duration-300;
  }

  .step-exit {
    @apply opacity-100 translate-x-0;
  }

  .step-exit-active {
    @apply opacity-0 -translate-x-4 transition-all duration-300;
  }

  /* Interactive Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(2deg); }
  }

  @keyframes slide-in {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes bounce-light {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
  }

  @keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(1deg); }
    75% { transform: rotate(-1deg); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4); }
    50% { box-shadow: 0 0 0 10px rgba(255, 215, 0, 0); }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-slide-in {
    animation: slide-in 0.6s ease-out forwards;
  }

  .animate-fade-in {
    animation: fade-in 0.4s ease-out forwards;
  }

  .animate-bounce-light {
    animation: bounce-light 2s ease-in-out infinite;
  }

  .animate-wiggle {
    animation: wiggle 0.5s ease-in-out;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s infinite;
  }

  /* Interactive hover effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px) rotate(2deg);
    box-shadow: 6px 6px 0px 0px rgba(0,0,0,1);
  }

  .hover-tilt {
    transition: all 0.3s ease;
  }

  .hover-tilt:hover {
    transform: rotate(3deg) scale(1.02);
  }

  .hover-glow {
    transition: all 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6), 4px 4px 0px 0px rgba(0,0,0,1);
  }

  /* Staggered animation delays */
  .delay-100 { animation-delay: 0.1s; }
  .delay-200 { animation-delay: 0.2s; }
  .delay-300 { animation-delay: 0.3s; }
  .delay-400 { animation-delay: 0.4s; }
  .delay-500 { animation-delay: 0.5s; }

  /* Loading states */
  .loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Scroll reveal animations */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }

  .scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}
