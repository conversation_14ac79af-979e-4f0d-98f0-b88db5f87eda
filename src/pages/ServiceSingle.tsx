import React from 'react';
import { useParams, Navigate } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import ServiceSingleContent from '../components/services/ServiceSingleContent';
import { getServiceById } from '../data/services';

const ServiceSingle = () => {
  const { serviceId } = useParams<{ serviceId: string }>();
  
  if (!serviceId) {
    return <Navigate to="/services" replace />;
  }

  const service = getServiceById(serviceId);
  
  if (!service) {
    return <Navigate to="/services" replace />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <ServiceSingleContent service={service} />
      </main>
      <Footer />
    </div>
  );
};

export default ServiceSingle;
