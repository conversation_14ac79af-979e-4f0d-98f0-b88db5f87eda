import React from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Award, Users, TrendingUp, Target } from 'lucide-react';

const About = () => {
  const achievements = [
    {
      icon: <Users size={40} />,
      number: "50+",
      label: "Companies Helped",
      description: "From startups to scale-ups"
    },
    {
      icon: <TrendingUp size={40} />,
      number: "200%",
      label: "Average Growth",
      description: "In lead generation"
    },
    {
      icon: <Award size={40} />,
      number: "8+",
      label: "Years Experience",
      description: "In marketing strategy"
    },
    {
      icon: <Target size={40} />,
      number: "95%",
      label: "Client Satisfaction",
      description: "Would recommend"
    }
  ];

  const skills = [
    "Growth Strategy",
    "Digital Marketing",
    "Lead Generation",
    "Marketing Automation",
    "Content Strategy",
    "Performance Marketing",
    "Brand Positioning",
    "Customer Acquisition"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-24">
        {/* Hero Section */}
        <section className="py-20 overflow-hidden">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
                  About Me
                </span>
                <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6">
                  Strategic Marketing <span className="relative inline-block">
                    Expert
                    <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
                      <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
                    </svg>
                  </span>
                </h1>
                <p className="text-xl mb-8">
                  I'm Asger Teglgaard, a strategic marketing consultant with over 8 years of experience 
                  helping startups and scale-ups achieve sustainable growth through data-driven strategies 
                  and innovative approaches.
                </p>
                <a href="/booking" className="neo-button bg-primary hover:bg-primary/90 text-black text-lg">
                  Book a Consultation
                </a>
              </div>
              
              <div className="relative">
                <div className="absolute -top-10 -left-10 w-40 h-40 bg-yellow neo-border rotate-12 animate-float"></div>
                <div className="absolute top-20 -right-5 w-24 h-24 bg-pink neo-border -rotate-6 animate-float [animation-delay:1s]"></div>
                <div className="neo-card overflow-hidden bg-white p-6 rotate-3 animate-fade-in">
                  <img 
                    src="/nerd.jpeg" 
                    alt="Asger Teglgaard" 
                    className="w-full h-full object-cover rounded"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Achievements Section */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 rotate-1">
                Track Record
              </span>
              <h2 className="text-3xl md:text-5xl font-bold">
                Proven Results
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {achievements.map((achievement, index) => (
                <div 
                  key={index}
                  className="neo-card p-6 bg-white text-center hover-lift"
                  style={{ 
                    transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
                  }}
                >
                  <div className="mb-4 text-primary flex justify-center">{achievement.icon}</div>
                  <div className="text-3xl font-bold mb-2">{achievement.number}</div>
                  <h3 className="text-lg font-bold mb-2">{achievement.label}</h3>
                  <p className="text-sm text-gray-600">{achievement.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Skills Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-pink text-white font-bold mb-4 -rotate-1">
                Expertise
              </span>
              <h2 className="text-3xl md:text-5xl font-bold">
                Core Competencies
              </h2>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              {skills.map((skill, index) => (
                <div 
                  key={index}
                  className="neo-card p-4 bg-white text-center hover-tilt"
                  style={{ 
                    transform: `rotate(${(index % 3 - 1) * 2}deg)`
                  }}
                >
                  <span className="font-bold">{skill}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <span className="inline-block px-4 py-2 neo-border bg-primary text-black font-bold mb-4 rotate-1">
                  My Story
                </span>
                <h2 className="text-3xl md:text-5xl font-bold">
                  From Marketing Manager to Strategic Consultant
                </h2>
              </div>
              
              <div className="neo-card p-8 bg-white -rotate-1">
                <div className="prose prose-lg max-w-none">
                  <p className="text-lg mb-6">
                    My journey in marketing began over 8 years ago when I discovered the power of 
                    combining creativity with data-driven insights. After being laid off from my 
                    marketing manager position, I decided to transform this challenge into an opportunity.
                  </p>
                  <p className="text-lg mb-6">
                    I've had the privilege of working with over 50 companies, from early-stage startups 
                    to established scale-ups, helping them navigate the complex world of modern marketing. 
                    My approach combines strategic thinking with practical implementation, ensuring that 
                    every recommendation can be acted upon immediately.
                  </p>
                  <p className="text-lg">
                    Today, I focus on helping businesses achieve sustainable growth through strategic 
                    marketing consulting, workshops, and speaking engagements. My goal is to make 
                    marketing strategy accessible and actionable for every business, regardless of size or industry.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default About;
