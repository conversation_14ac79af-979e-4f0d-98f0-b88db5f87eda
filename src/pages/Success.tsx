import React from 'react';
import { Check, Calendar, Mail, ArrowRight } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const Success = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            {/* Success Icon */}
            <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-8">
              <Check className="h-10 w-10 text-white" />
            </div>

            {/* Success Message */}
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Payment Successful!
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              Thank you for your order! Your landing page design project has been confirmed.
            </p>

            {/* Order Details */}
            <div className="neo-card p-8 bg-white mb-8 text-left">
              <h2 className="text-xl font-bold mb-4">Order Summary</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Landing Page Design</span>
                  <span className="font-bold">$2,500</span>
                </div>
                <div className="flex justify-between">
                  <span>Estimated Delivery</span>
                  <span className="font-bold">21 Days</span>
                </div>
                <div className="border-t pt-3 flex justify-between">
                  <span className="font-bold">Total Paid</span>
                  <span className="font-bold text-green-600">$2,500</span>
                </div>
              </div>
            </div>

            {/* Next Steps */}
            <div className="neo-card p-8 bg-primary text-left mb-8">
              <h2 className="text-xl font-bold mb-4">What Happens Next?</h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                    1
                  </div>
                  <div>
                    <h3 className="font-bold">Confirmation Email</h3>
                    <p className="text-sm">You'll receive a confirmation email with your order details within 5 minutes.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                    2
                  </div>
                  <div>
                    <h3 className="font-bold">Kick-off Call Scheduling</h3>
                    <p className="text-sm">Our team will reach out within 24 hours to schedule your kick-off call.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                    3
                  </div>
                  <div>
                    <h3 className="font-bold">Design Process Begins</h3>
                    <p className="text-sm">After the kick-off call, our design team will start working on your landing page.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="neo-button bg-secondary text-white hover:bg-secondary/90 px-8 py-3">
                <Calendar className="h-5 w-5 mr-2" />
                Schedule Kick-off Call
              </button>
              <button className="neo-button bg-white hover:bg-gray-50 text-black px-8 py-3">
                <Mail className="h-5 w-5 mr-2" />
                Contact Support
              </button>
            </div>

            {/* Additional Info */}
            <div className="mt-12 text-center">
              <p className="text-gray-600 mb-4">
                Have questions? We're here to help!
              </p>
              <a 
                href="mailto:<EMAIL>" 
                className="text-blue-600 hover:underline font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Success;
