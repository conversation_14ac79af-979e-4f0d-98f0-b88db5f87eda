// Stripe API utilities for client-side integration

export interface PaymentIntentData {
  amount: number; // in cents
  currency: string;
  customerEmail: string;
  customerName: string;
  serviceId: string;
  serviceName: string;
  projectData: Record<string, any>; // All checkout form data
}

export interface ConsultationRequestData {
  service: {
    id: string;
    title: string;
    type: string;
  };
  customer: {
    name: string;
    email: string;
    company?: string;
    phone?: string;
  };
  project: {
    description: string;
    timeline?: string;
    budget?: string;
    additionalInfo?: string;
  };
  serviceSpecific: Record<string, any>;
  submittedAt: string;
  status: 'pending_review' | 'reviewed' | 'proposal_sent' | 'accepted' | 'declined';
}

export const createPaymentIntent = async (data: PaymentIntentData) => {
  try {
    // In a real application, this would call your backend API
    // For now, we'll simulate the API call
    const response = await fetch('/api/create-payment-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to create payment intent');
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw error;
  }
};

export const confirmPayment = async (paymentIntentId: string, paymentMethodId: string) => {
  try {
    const response = await fetch('/api/confirm-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        paymentIntentId,
        paymentMethodId,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to confirm payment');
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error confirming payment:', error);
    throw error;
  }
};

// Consultation Request API
export const submitConsultationRequest = async (data: ConsultationRequestData) => {
  try {
    // In a real application, this would call your backend API
    const response = await fetch('/api/consultation-requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to submit consultation request');
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error submitting consultation request:', error);
    throw error;
  }
};

// Mock functions for development (remove when implementing real backend)
export const mockCreatePaymentIntent = async (data: PaymentIntentData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  return {
    clientSecret: 'pi_mock_client_secret_' + Math.random().toString(36).substr(2, 9),
    paymentIntentId: 'pi_mock_' + Math.random().toString(36).substr(2, 9),
  };
};

export const mockConfirmPayment = async (paymentIntentId: string) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  return {
    success: true,
    paymentIntentId,
    status: 'succeeded',
  };
};

export const mockSubmitConsultationRequest = async (data: ConsultationRequestData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));

  return {
    success: true,
    requestId: 'req_mock_' + Math.random().toString(36).substr(2, 9),
    status: 'pending_review',
    estimatedResponseTime: '24 hours',
  };
};
