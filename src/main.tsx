import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { PostHogProvider } from 'posthog-js/react'
import App from './App.tsx'
import './index.css'
import { dynamicFaviconService } from './services/dynamicFaviconService'

// PostHog configuration
const posthogKey = import.meta.env.VITE_PUBLIC_POSTHOG_KEY
const posthogHost = import.meta.env.VITE_PUBLIC_POSTHOG_HOST

// Debug logging for development
if (import.meta.env.DEV) {
  console.log('PostHog Config:', {
    key: posthogKey ? `${posthogKey.substring(0, 8)}...` : 'MISSING',
    host: posthogHost || 'MISSING'
  })
}

const options = {
  api_host: posthogHost || 'https://eu.i.posthog.com',
  person_profiles: 'identified_only',
  // Disable in development if no key is provided
  disabled: !posthogKey,
}

// Only render PostHogProvider if we have a valid API key
const AppWithAnalytics = posthogKey ? (
  <PostHogProvider apiKey={posthogKey} options={options}>
    <App />
  </PostHogProvider>
) : (
  <App />
)

// Start dynamic favicon service
dynamicFaviconService.start();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    {AppWithAnalytics}
  </StrictMode>
);
