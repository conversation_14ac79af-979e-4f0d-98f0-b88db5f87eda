
import { fetchWebsiteMetadata } from '../api/website-metadata';
import { analyzeWebsite } from '../api/analyze-website';

// Set up fetch interceptor for API calls
export const setupApiInterceptor = () => {
  const originalFetch = window.fetch;
  
  window.fetch = async function(input, init) {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.toString() : input.url;
    
    // Intercept calls to our simulated API endpoints
    if (url === '/api/website-metadata' && init?.method === 'POST') {
      const body = JSON.parse(init.body as string);
      const result = await fetchWebsiteMetadata(body.url);
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    if (url === '/api/analyze-website' && init?.method === 'POST') {
      const body = JSON.parse(init.body as string);
      const result = await analyzeWebsite(body.url);
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Pass through to original fetch for all other calls
    return originalFetch.apply(this, [input, init]);
  };
};
