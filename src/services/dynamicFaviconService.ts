/**
 * Dynamic Favicon Service
 * 
 * This service generates and updates favicons dynamically based on time,
 * using the same logic as the InteractiveLogo component.
 */

export type OnlineStatus = 'online' | 'away' | 'offline';

export class DynamicFaviconService {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private updateInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.canvas.width = 32;
    this.canvas.height = 32;
    this.ctx = this.canvas.getContext('2d')!;
  }

  /**
   * Calculate online status based on current time
   * Same logic as InteractiveLogo component
   */
  private getOnlineStatus(): OnlineStatus {
    const now = new Date();
    const hour = now.getHours();

    if (hour >= 8 && hour < 17) {
      return 'online';
    } else if ((hour >= 6 && hour < 8) || (hour >= 17 && hour < 22)) {
      return 'away';
    } else {
      return 'offline';
    }
  }

  /**
   * Get status color hex value
   */
  private getStatusColor(status: OnlineStatus): string {
    switch (status) {
      case 'online':
        return '#10b981'; // green-500
      case 'away':
        return '#eab308'; // yellow-500
      case 'offline':
        return '#ef4444'; // red-500
      default:
        return '#6b7280'; // gray-500
    }
  }

  /**
   * Generate a favicon with the specified color
   */
  private generateFavicon(color: string): string {
    // Clear canvas
    this.ctx.clearRect(0, 0, 32, 32);

    // Draw main circle (indicator)
    this.ctx.beginPath();
    this.ctx.arc(16, 16, 12, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();

    // Add black border for better visibility
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Add white inner highlight for depth
    this.ctx.beginPath();
    this.ctx.arc(13, 13, 3, 0, 2 * Math.PI);
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.fill();

    // Convert canvas to data URL
    return this.canvas.toDataURL('image/png');
  }

  /**
   * Update the favicon in the document head
   */
  private updateFaviconInDOM(dataUrl: string): void {
    // Remove existing favicon links
    const existingLinks = document.querySelectorAll('link[rel*="icon"]');
    existingLinks.forEach(link => link.remove());

    // Create new favicon link
    const link = document.createElement('link');
    link.rel = 'icon';
    link.type = 'image/png';
    link.href = dataUrl;
    
    // Add to document head
    document.head.appendChild(link);

    // Also create a shortcut icon for better browser support
    const shortcutLink = document.createElement('link');
    shortcutLink.rel = 'shortcut icon';
    shortcutLink.type = 'image/png';
    shortcutLink.href = dataUrl;
    document.head.appendChild(shortcutLink);
  }

  /**
   * Update favicon based on current status
   */
  private updateFavicon(): void {
    const status = this.getOnlineStatus();
    const color = this.getStatusColor(status);
    const faviconDataUrl = this.generateFavicon(color);
    this.updateFaviconInDOM(faviconDataUrl);

    // Log status change for debugging
    console.log(`Favicon updated: ${status} (${color})`);
  }

  /**
   * Start the dynamic favicon service
   */
  public start(): void {
    // Update immediately
    this.updateFavicon();

    // Update every minute (same as InteractiveLogo)
    this.updateInterval = setInterval(() => {
      this.updateFavicon();
    }, 60000);

    console.log('Dynamic favicon service started');
  }

  /**
   * Stop the dynamic favicon service
   */
  public stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    console.log('Dynamic favicon service stopped');
  }

  /**
   * Get current status (for debugging)
   */
  public getCurrentStatus(): { status: OnlineStatus; color: string } {
    const status = this.getOnlineStatus();
    const color = this.getStatusColor(status);
    return { status, color };
  }

  /**
   * Manually trigger favicon update (for testing)
   */
  public forceUpdate(): void {
    this.updateFavicon();
  }
}

// Create singleton instance
export const dynamicFaviconService = new DynamicFaviconService();
