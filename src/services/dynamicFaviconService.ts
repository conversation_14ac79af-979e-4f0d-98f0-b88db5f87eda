/**
 * Dynamic Favicon Service
 * 
 * This service generates and updates favicons dynamically based on time,
 * using the same logic as the InteractiveLogo component.
 */

export type OnlineStatus = 'online' | 'away' | 'offline';

export class DynamicFaviconService {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private updateInterval: NodeJS.Timeout | null = null;
  private animationInterval: NodeJS.Timeout | null = null;
  private profileImage: HTMLImageElement | null = null;
  private showingProfile: boolean = false;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.canvas.width = 32;
    this.canvas.height = 32;
    this.ctx = this.canvas.getContext('2d')!;
    this.loadProfileImage();
  }

  /**
   * Load the profile image for animation
   */
  private loadProfileImage(): void {
    this.profileImage = new Image();
    this.profileImage.crossOrigin = 'anonymous';
    this.profileImage.onload = () => {
      console.log('Profile image loaded for favicon animation');
    };
    this.profileImage.onerror = () => {
      console.warn('Failed to load profile image for favicon animation');
    };
    this.profileImage.src = '/me.png';
  }

  /**
   * Calculate online status based on current time
   * Same logic as InteractiveLogo component
   */
  private getOnlineStatus(): OnlineStatus {
    const now = new Date();
    const hour = now.getHours();

    if (hour >= 8 && hour < 17) {
      return 'online';
    } else if ((hour >= 6 && hour < 8) || (hour >= 17 && hour < 22)) {
      return 'away';
    } else {
      return 'offline';
    }
  }

  /**
   * Get status color hex value
   */
  private getStatusColor(status: OnlineStatus): string {
    switch (status) {
      case 'online':
        return '#10b981'; // green-500
      case 'away':
        return '#eab308'; // yellow-500
      case 'offline':
        return '#ef4444'; // red-500
      default:
        return '#6b7280'; // gray-500
    }
  }

  /**
   * Generate a status indicator favicon with the specified color
   */
  private generateStatusFavicon(color: string): string {
    // Clear canvas
    this.ctx.clearRect(0, 0, 32, 32);

    // Draw main circle (indicator)
    this.ctx.beginPath();
    this.ctx.arc(16, 16, 12, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();

    // Add black border for better visibility
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Add white inner highlight for depth
    this.ctx.beginPath();
    this.ctx.arc(13, 13, 3, 0, 2 * Math.PI);
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.fill();

    // Convert canvas to data URL
    return this.canvas.toDataURL('image/png');
  }

  /**
   * Generate a profile picture favicon
   */
  private generateProfileFavicon(): string {
    // Clear canvas
    this.ctx.clearRect(0, 0, 32, 32);

    if (this.profileImage && this.profileImage.complete) {
      // Create circular clipping path
      this.ctx.save();
      this.ctx.beginPath();
      this.ctx.arc(16, 16, 14, 0, 2 * Math.PI);
      this.ctx.clip();

      // Draw profile image
      this.ctx.drawImage(this.profileImage, 2, 2, 28, 28);
      this.ctx.restore();

      // Add black border
      this.ctx.beginPath();
      this.ctx.arc(16, 16, 14, 0, 2 * Math.PI);
      this.ctx.strokeStyle = '#000000';
      this.ctx.lineWidth = 2;
      this.ctx.stroke();
    } else {
      // Fallback to status indicator if image not loaded
      const status = this.getOnlineStatus();
      const color = this.getStatusColor(status);
      return this.generateStatusFavicon(color);
    }

    // Convert canvas to data URL
    return this.canvas.toDataURL('image/png');
  }

  /**
   * Update the favicon in the document head
   */
  private updateFaviconInDOM(dataUrl: string): void {
    // Remove existing favicon links
    const existingLinks = document.querySelectorAll('link[rel*="icon"]');
    existingLinks.forEach(link => link.remove());

    // Create new favicon link
    const link = document.createElement('link');
    link.rel = 'icon';
    link.type = 'image/png';
    link.href = dataUrl;
    
    // Add to document head
    document.head.appendChild(link);

    // Also create a shortcut icon for better browser support
    const shortcutLink = document.createElement('link');
    shortcutLink.rel = 'shortcut icon';
    shortcutLink.type = 'image/png';
    shortcutLink.href = dataUrl;
    document.head.appendChild(shortcutLink);
  }

  /**
   * Update favicon based on current status and animation state
   */
  private updateFavicon(): void {
    let faviconDataUrl: string;

    if (this.showingProfile) {
      faviconDataUrl = this.generateProfileFavicon();
    } else {
      const status = this.getOnlineStatus();
      const color = this.getStatusColor(status);
      faviconDataUrl = this.generateStatusFavicon(color);
    }

    this.updateFaviconInDOM(faviconDataUrl);

    // Log status change for debugging
    const status = this.getOnlineStatus();
    console.log(`Favicon updated: ${this.showingProfile ? 'profile' : status} (${this.getStatusColor(status)})`);
  }

  /**
   * Start the animation cycle between status indicator and profile picture
   */
  private startAnimation(): void {
    // Stop any existing animation
    this.stopAnimation();

    // Animate every 3 seconds (3s status, 3s profile, repeat)
    this.animationInterval = setInterval(() => {
      this.showingProfile = !this.showingProfile;
      this.updateFavicon();
    }, 3000);

    console.log('Favicon animation started (3s intervals)');
  }

  /**
   * Stop the animation cycle
   */
  private stopAnimation(): void {
    if (this.animationInterval) {
      clearInterval(this.animationInterval);
      this.animationInterval = null;
    }
  }

  /**
   * Start the dynamic favicon service with animation
   */
  public start(): void {
    // Update immediately
    this.updateFavicon();

    // Start animation cycle
    this.startAnimation();

    // Update every minute to check for status changes (same as InteractiveLogo)
    this.updateInterval = setInterval(() => {
      // Only update if we're currently showing the status indicator
      if (!this.showingProfile) {
        this.updateFavicon();
      }
    }, 60000);

    console.log('Dynamic favicon service started with animation');
  }

  /**
   * Stop the dynamic favicon service
   */
  public stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.stopAnimation();
    console.log('Dynamic favicon service stopped');
  }

  /**
   * Get current status (for debugging)
   */
  public getCurrentStatus(): { status: OnlineStatus; color: string } {
    const status = this.getOnlineStatus();
    const color = this.getStatusColor(status);
    return { status, color };
  }

  /**
   * Manually trigger favicon update (for testing)
   */
  public forceUpdate(): void {
    this.updateFavicon();
  }

  /**
   * Toggle animation on/off
   */
  public toggleAnimation(): void {
    if (this.animationInterval) {
      this.stopAnimation();
      // Reset to status indicator when stopping animation
      this.showingProfile = false;
      this.updateFavicon();
    } else {
      this.startAnimation();
    }
  }

  /**
   * Get animation state (for debugging)
   */
  public getAnimationState(): { isAnimating: boolean; showingProfile: boolean } {
    return {
      isAnimating: this.animationInterval !== null,
      showingProfile: this.showingProfile
    };
  }
}

// Create singleton instance
export const dynamicFaviconService = new DynamicFaviconService();
