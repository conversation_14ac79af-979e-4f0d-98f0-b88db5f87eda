import { neon } from '@neondatabase/serverless';

// Get the database URL from environment variables
const DATABASE_URL = import.meta.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

if (!DATABASE_URL) {
  throw new Error('DATABASE_URL is not set. Please add it to your .env file.');
}

// Create the Neon SQL client
export const sql = neon(DATABASE_URL);

// Example usage:
// const result = await sql`SELECT * FROM users`;
// console.log(result);
