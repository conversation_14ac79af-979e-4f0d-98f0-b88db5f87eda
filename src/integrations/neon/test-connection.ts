import { sql } from './client';

export async function testNeonConnection() {
  try {
    console.log('Testing Neon database connection...');
    
    // Test basic connection with a simple query
    const result = await sql`SELECT version()`;
    console.log('✅ Neon connection successful!');
    console.log('Database version:', result[0]?.version);
    
    // Test current time
    const timeResult = await sql`SELECT NOW() as current_time`;
    console.log('Current database time:', timeResult[0]?.current_time);
    
    return { success: true, version: result[0]?.version };
  } catch (error) {
    console.error('❌ Neon connection failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Example of how to create a simple table
export async function createUsersTable() {
  try {
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Users table created successfully');
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to create users table:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Example of inserting data
export async function insertUser(email: string, name: string) {
  try {
    const result = await sql`
      INSERT INTO users (email, name)
      VALUES (${email}, ${name})
      RETURNING *
    `;
    console.log('✅ User inserted successfully:', result[0]);
    return { success: true, user: result[0] };
  } catch (error) {
    console.error('❌ Failed to insert user:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Example of querying data
export async function getUsers() {
  try {
    const result = await sql`SELECT * FROM users ORDER BY created_at DESC`;
    console.log('✅ Users retrieved successfully:', result);
    return { success: true, users: result };
  } catch (error) {
    console.error('❌ Failed to get users:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}
