// Database types for your Neon PostgreSQL database
// Add your table types here as you create them

export interface User {
  id: number;
  email: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseError {
  message: string;
  code?: string;
  details?: string;
}

// Example table structure - modify according to your needs
export interface Tables {
  users: User;
  // Add more tables here as needed
}

// Helper type for database operations
export type DatabaseResult<T> = T[] | DatabaseError;
