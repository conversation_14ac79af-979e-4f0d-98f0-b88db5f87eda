# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here

# Neon Database Configuration
# Copy this file to .env and replace with your actual Neon connection string
DATABASE_URL="postgresql://[user]:[password]@[neon_hostname]/[dbname]?sslmode=require&channel_binding=require"

# Vite Environment Variables (must be prefixed with VITE_)
VITE_DATABASE_URL="postgresql://[user]:[password]@[neon_hostname]/[dbname]?sslmode=require&channel_binding=require"

# App Configuration
VITE_APP_URL=http://localhost:8082

# PostHog Analytics Configuration
VITE_PUBLIC_POSTHOG_KEY=phc_your_posthog_key_here
VITE_PUBLIC_POSTHOG_HOST=https://eu.i.posthog.com

# Example format:
# DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
# VITE_DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
