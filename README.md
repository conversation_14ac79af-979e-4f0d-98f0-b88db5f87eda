# Asger.me - Personal Brand & Marketing Services Platform

## Project Overview

**Asger.me** is a personal brand website that serves as both an online resume and marketing services platform. Built with modern web technologies and featuring a distinctive neo-brutalist design aesthetic.

**Live URL**: https://asger.me

## Development

### Local Development Setup

**Prerequisites**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

### Alternative Development Methods

#### GitHub Codespaces
- Navigate to the main page of your repository
- Click on the "Code" button (green button) near the top right
- Select the "Codespaces" tab
- Click on "New codespace" to launch a new Codespace environment
- Edit files directly within the Codespace and commit and push your changes once you're done

#### Direct GitHub Editing
- Navigate to the desired file(s)
- Click the "Edit" button (pencil icon) at the top right of the file view
- Make your changes and commit the changes

## Technology Stack

This project is built with modern web technologies:

- **Frontend Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with custom neo-brutalist design system
- **UI Components**: shadcn/ui
- **Routing**: React Router DOM
- **Payment Processing**: Stripe integration
- **Database**: Neon (PostgreSQL)
- **Deployment**: Netlify/Vercel

## Features

- **Personal Brand Showcase**: Professional portfolio and resume
- **Service Offerings**: Marketing consulting, workshops, and speaking
- **Booking System**: Integrated calendar and payment processing
- **Neo-Brutalist Design**: Bold, accessible, and memorable aesthetic
- **Content Management**: Blog and resource sections
- **Mobile-First**: Responsive design optimized for all devices

## Deployment

The project can be deployed to various platforms:

- **Netlify**: Recommended for static hosting with form handling
- **Vercel**: Excellent for React applications with serverless functions
- **Custom Domain**: Configure your domain through your hosting provider
